# Task 06: Permission Helpers and Validation Utilities

## Objective
Create comprehensive helper classes, validation utilities, and supporting infrastructure for the RBAC system to ensure data integrity, security, and ease of use.

## Prerequisites
- Task 04 (Permission Models and Repository) completed
- Task 05 (Permission Service and Caching) completed
- Understanding of ProManage validation patterns
- Access to existing helper class patterns

## Scope
Implement supporting utilities and helpers:
1. Advanced permission validation and business rules
2. Permission helper methods for common operations
3. Security validation and audit utilities
4. Form registration and discovery helpers
5. Permission debugging and diagnostic tools

## Deliverables

### 1. Advanced Permission Validation
**File**: `Modules/Helpers/Permissions/PermissionBusinessRules.cs`

#### Business Rule Validation
```csharp
public static class PermissionBusinessRules
{
    // Core business rules
    public static ValidationResult ValidatePermissionAssignment(int userId, string formName, string permissionType, bool isGranted);
    public static ValidationResult ValidateRolePermissionChange(int roleId, string formName, string permissionType, bool isGranted);
    public static ValidationResult ValidateGlobalRestriction(int userId, string permissionType, bool isGranted);
    
    // Security rules
    public static bool CanUserManagePermissions(int userId, int targetUserId);
    public static bool CanUserManageRole(int userId, int roleId);
    public static bool IsPermissionChangeAllowed(int userId, string formName, string permissionType);
    
    // Hierarchy validation
    public static ValidationResult ValidatePermissionHierarchy(int userId, string formName);
    public static List<string> GetPermissionConflicts(int userId);
    public static bool WouldCreateCircularDependency(int roleId, int parentRoleId);
    
    public class ValidationResult
    {
        public bool IsValid { get; set; }
        public string ErrorMessage { get; set; }
        public List<string> Warnings { get; set; }
        public List<string> Recommendations { get; set; }
        public SecurityLevel SecurityImpact { get; set; }
    }
    
    public enum SecurityLevel
    {
        None,
        Low,
        Medium,
        High,
        Critical
    }
}
```

### 2. Permission Helper Utilities
**File**: `Modules/Helpers/Permissions/PermissionHelper.cs`

#### Common Operations
```csharp
public static class PermissionHelper
{
    // Permission checking shortcuts
    public static bool CanCurrentUserAccess(string formName);
    public static bool CanCurrentUserEdit(string formName);
    public static bool CanCurrentUserCreate(string formName);
    public static bool CanCurrentUserDelete(string formName);
    
    // Bulk permission operations
    public static Dictionary<string, bool> GetFormPermissions(int userId, string formName);
    public static List<string> GetAccessibleForms(int userId);
    public static List<string> GetEditableForms(int userId);
    
    // Permission comparison
    public static PermissionDifference CompareUserPermissions(int userId1, int userId2);
    public static PermissionDifference CompareRolePermissions(int roleId1, int roleId2);
    
    // Permission inheritance
    public static List<PermissionInheritance> GetPermissionInheritance(int userId, string formName);
    public static bool IsPermissionInherited(int userId, string formName, string permissionType);
    
    public class PermissionDifference
    {
        public List<string> OnlyInFirst { get; set; }
        public List<string> OnlyInSecond { get; set; }
        public List<string> Different { get; set; }
        public List<string> Same { get; set; }
    }
    
    public class PermissionInheritance
    {
        public string FormName { get; set; }
        public string PermissionType { get; set; }
        public bool IsGranted { get; set; }
        public string Source { get; set; }
        public int Level { get; set; }
        public string Description { get; set; }
    }
}
```

### 3. Form Registration Helper
**File**: `Modules/Helpers/Permissions/FormRegistrationHelper.cs`

#### Dynamic Form Discovery
```csharp
public static class FormRegistrationHelper
{
    // Form discovery
    public static List<FormInfo> DiscoverPermissionControlledForms();
    public static bool RegisterForm(string formName, string displayName, string category);
    public static bool UnregisterForm(string formName);
    
    // Form validation
    public static bool IsFormRegistered(string formName);
    public static bool IsFormActive(string formName);
    public static ValidationResult ValidateFormRegistration(FormInfo formInfo);
    
    // Attribute-based registration
    public static void RegisterFormsFromAssembly(Assembly assembly);
    public static List<FormInfo> GetFormsWithAttribute<T>() where T : Attribute;
    
    public class FormInfo
    {
        public string FormName { get; set; }
        public string DisplayName { get; set; }
        public string Category { get; set; }
        public Type FormType { get; set; }
        public bool RequiresPermissions { get; set; }
        public List<string> RequiredPermissions { get; set; }
        public string Description { get; set; }
    }
    
    // Form attribute for automatic registration
    [AttributeUsage(AttributeTargets.Class)]
    public class PermissionRequiredAttribute : Attribute
    {
        public string FormName { get; }
        public string DisplayName { get; }
        public string Category { get; set; } = "business";
        public string Description { get; set; }
        
        public PermissionRequiredAttribute(string formName, string displayName)
        {
            FormName = formName;
            DisplayName = displayName;
        }
    }
}
```

### 4. Permission Audit Helper
**File**: `Modules/Helpers/Permissions/PermissionAuditHelper.cs`

#### Audit and Logging
```csharp
public static class PermissionAuditHelper
{
    // Audit logging
    public static void LogPermissionCheck(int userId, string formName, string permissionType, bool result);
    public static void LogPermissionChange(int userId, string formName, string permissionType, bool oldValue, bool newValue, int changedByUserId);
    public static void LogRoleChange(int userId, int oldRoleId, int newRoleId, int changedByUserId);
    
    // Audit queries
    public static List<PermissionAuditEntry> GetUserPermissionHistory(int userId, DateTime? fromDate = null);
    public static List<PermissionAuditEntry> GetFormPermissionHistory(string formName, DateTime? fromDate = null);
    public static List<PermissionAuditEntry> GetPermissionChanges(DateTime fromDate, DateTime toDate);
    
    // Security monitoring
    public static List<SecurityEvent> GetSecurityEvents(DateTime fromDate, DateTime toDate);
    public static bool DetectSuspiciousActivity(int userId);
    public static List<string> GetFailedPermissionAttempts(int userId, TimeSpan timeWindow);
    
    public class PermissionAuditEntry
    {
        public int AuditId { get; set; }
        public int UserId { get; set; }
        public string Username { get; set; }
        public string FormName { get; set; }
        public string PermissionType { get; set; }
        public string Action { get; set; } // "CHECK", "GRANT", "DENY", "CHANGE"
        public bool? OldValue { get; set; }
        public bool? NewValue { get; set; }
        public int? ChangedByUserId { get; set; }
        public string ChangedByUsername { get; set; }
        public DateTime Timestamp { get; set; }
        public string IPAddress { get; set; }
        public string UserAgent { get; set; }
    }
    
    public class SecurityEvent
    {
        public int EventId { get; set; }
        public string EventType { get; set; }
        public string Description { get; set; }
        public int UserId { get; set; }
        public string Username { get; set; }
        public DateTime Timestamp { get; set; }
        public SecurityLevel Severity { get; set; }
        public Dictionary<string, object> Metadata { get; set; }
    }
}
```

### 5. Permission Debugging Tools
**File**: `Modules/Helpers/Permissions/PermissionDebugHelper.cs`

#### Debugging and Diagnostics
```csharp
public static class PermissionDebugHelper
{
    // Permission resolution debugging
    public static PermissionResolutionTrace TracePermissionResolution(int userId, string formName, string permissionType);
    public static List<PermissionStep> GetPermissionSteps(int userId, string formName, string permissionType);
    public static string GeneratePermissionReport(int userId);
    
    // System diagnostics
    public static PermissionSystemDiagnostics RunSystemDiagnostics();
    public static List<string> ValidatePermissionIntegrity();
    public static Dictionary<string, object> GetSystemStatistics();
    
    // Performance diagnostics
    public static PerformanceDiagnostics GetPerformanceDiagnostics();
    public static void BenchmarkPermissionChecks(int iterations = 1000);
    
    public class PermissionResolutionTrace
    {
        public int UserId { get; set; }
        public string FormName { get; set; }
        public string PermissionType { get; set; }
        public bool FinalResult { get; set; }
        public List<PermissionStep> Steps { get; set; }
        public TimeSpan ResolutionTime { get; set; }
        public bool UsedCache { get; set; }
    }
    
    public class PermissionStep
    {
        public int Level { get; set; }
        public string Source { get; set; }
        public bool? Result { get; set; }
        public string Reason { get; set; }
        public bool IsOverride { get; set; }
    }
    
    public class PermissionSystemDiagnostics
    {
        public bool DatabaseConnectivity { get; set; }
        public bool ServiceInitialized { get; set; }
        public bool CacheOperational { get; set; }
        public int TotalUsers { get; set; }
        public int TotalRoles { get; set; }
        public int TotalPermissions { get; set; }
        public List<string> Issues { get; set; }
        public List<string> Warnings { get; set; }
    }
}
```

### 6. Permission Extension Methods
**File**: `Modules/Helpers/Permissions/PermissionExtensions.cs`

#### Extension Methods for Convenience
```csharp
public static class PermissionExtensions
{
    // User extensions
    public static bool HasPermission(this UserModel user, string formName, string permissionType)
    {
        return PermissionService.Instance.HasPermission(user.UserId, formName, permissionType);
    }
    
    public static List<string> GetVisibleForms(this UserModel user)
    {
        return PermissionService.Instance.GetVisibleForms(user.UserId);
    }
    
    public static bool CanAccess(this UserModel user, string formName)
    {
        return user.HasPermission(formName, PermissionConstants.PermissionTypes.Read);
    }
    
    // Form extensions
    public static bool IsAccessibleBy(this Form form, int userId)
    {
        var formName = form.GetType().Name;
        return PermissionService.Instance.HasPermission(userId, formName, PermissionConstants.PermissionTypes.Read);
    }
    
    public static void ApplyPermissions(this Form form, int userId)
    {
        var formName = form.GetType().Name;
        
        // Apply read permission
        if (!PermissionService.Instance.HasPermission(userId, formName, PermissionConstants.PermissionTypes.Read))
        {
            form.Close();
            return;
        }
        
        // Apply edit permissions to common controls
        ApplyEditPermissions(form, userId, formName);
    }
    
    private static void ApplyEditPermissions(Form form, int userId, string formName)
    {
        var canEdit = PermissionService.Instance.HasPermission(userId, formName, PermissionConstants.PermissionTypes.Edit);
        var canNew = PermissionService.Instance.HasPermission(userId, formName, PermissionConstants.PermissionTypes.New);
        var canDelete = PermissionService.Instance.HasPermission(userId, formName, PermissionConstants.PermissionTypes.Delete);
        
        // Apply to common button names
        SetControlEnabled(form, "btnSave", canEdit);
        SetControlEnabled(form, "btnEdit", canEdit);
        SetControlEnabled(form, "btnNew", canNew);
        SetControlEnabled(form, "btnDelete", canDelete);
        SetControlEnabled(form, "BarButtonItemSave", canEdit);
        SetControlEnabled(form, "BarButtonItemNew", canNew);
        SetControlEnabled(form, "BarButtonItemDelete", canDelete);
    }
    
    private static void SetControlEnabled(Control parent, string controlName, bool enabled)
    {
        var control = FindControlByName(parent, controlName);
        if (control != null)
        {
            control.Enabled = enabled;
        }
    }
    
    private static Control FindControlByName(Control parent, string name)
    {
        if (parent.Name == name) return parent;
        
        foreach (Control child in parent.Controls)
        {
            var found = FindControlByName(child, name);
            if (found != null) return found;
        }
        
        return null;
    }
}
```

## Acceptance Criteria

### Business Rules Implementation
- [ ] Comprehensive validation for all permission operations
- [ ] Security rules prevent unauthorized changes
- [ ] Hierarchy validation prevents conflicts
- [ ] Clear error messages and recommendations

### Helper Utilities
- [ ] Common permission operations simplified
- [ ] Bulk operations for efficiency
- [ ] Permission comparison and analysis tools
- [ ] Extension methods for ease of use

### Form Registration
- [ ] Dynamic form discovery working
- [ ] Attribute-based registration functional
- [ ] Form validation and integrity checks
- [ ] Support for future form additions

### Audit and Security
- [ ] Comprehensive audit logging
- [ ] Security event monitoring
- [ ] Suspicious activity detection
- [ ] Historical permission tracking

### Debugging Tools
- [ ] Permission resolution tracing
- [ ] System diagnostics and validation
- [ ] Performance monitoring and benchmarking
- [ ] Clear diagnostic reports

## Integration Points

### Existing System
- Uses existing validation patterns from ProManage
- Integrates with current logging infrastructure
- Compatible with existing helper class organization
- Follows established error handling patterns

### Future Tasks
- Provides utilities for Task 07 (MainFrame integration)
- Supports Task 08 (UserMasterForm integration)
- Enables Task 09 (PermissionManagementForm)
- Establishes foundation for all permission UI components

## Testing Requirements

### Unit Testing
```csharp
[Test]
public void ValidatePermissionAssignment_ValidRequest_ReturnsValid()
{
    var result = PermissionBusinessRules.ValidatePermissionAssignment(1, "EstimateForm", "Read", true);
    Assert.IsTrue(result.IsValid);
}

[Test]
public void CanCurrentUserAccess_ValidForm_ReturnsCorrectResult()
{
    var result = PermissionHelper.CanCurrentUserAccess("EstimateForm");
    Assert.IsNotNull(result);
}
```

### Integration Testing
- Test helper integration with permission service
- Validate audit logging functionality
- Test form registration and discovery
- Verify extension methods work correctly

### Performance Testing
- Test helper method performance
- Validate audit logging performance
- Test bulk operations efficiency
- Monitor memory usage of utilities

## Implementation Notes

### Security Considerations
- Validate all input parameters
- Prevent unauthorized permission changes
- Audit all permission-related operations
- Implement proper access controls

### Performance Optimization
- Cache frequently used validation results
- Optimize bulk operations
- Minimize database calls in helpers
- Use efficient data structures

### Maintainability
- Clear separation of concerns
- Comprehensive documentation
- Consistent error handling
- Extensible design for future needs

## Files to Create/Modify

### New Files (6 files)
- `Modules/Helpers/Permissions/PermissionBusinessRules.cs`
- `Modules/Helpers/Permissions/PermissionHelper.cs`
- `Modules/Helpers/Permissions/FormRegistrationHelper.cs`
- `Modules/Helpers/Permissions/PermissionAuditHelper.cs`
- `Modules/Helpers/Permissions/PermissionDebugHelper.cs`
- `Modules/Helpers/Permissions/PermissionExtensions.cs`

## Dependencies
- Task 04: Permission Models and Repository (REQUIRED)
- Task 05: Permission Service and Caching (REQUIRED)

## Estimated Effort
- 6-8 hours for business rules and validation
- 4-6 hours for helper utilities
- 4-6 hours for audit and security features
- 4-6 hours for debugging tools and extensions
- 2-4 hours for testing and validation
