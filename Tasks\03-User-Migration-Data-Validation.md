# Task 03: User Migration and Data Validation

## Objective
Migrate existing users from the legacy role system (varchar role field) to the new RBAC system (role_id foreign key) while maintaining data integrity and backward compatibility.

## Prerequisites
- Task 01 (RBAC Database Schema Setup) completed
- Task 02 (Default Data Population) completed
- Existing users table with role varchar field analyzed
- Database backup created before migration

## Scope
Safely migrate existing user data to the new RBAC system:
1. Analyze current user role distribution
2. Map existing role values to new role_id system
3. Populate role_id for all existing users
4. Validate migration integrity
5. Establish data consistency checks

## Deliverables

### 1. User Migration Script
**File**: `Modules/Procedures/Permissions/03-User-Migration.sql`

**Content Requirements**:
- Pre-migration analysis and validation
- Safe role mapping with fallback defaults
- Transaction-wrapped migration process
- Post-migration verification
- Rollback procedures for safety

### 2. Pre-Migration Analysis

#### Current User Analysis Query
```sql
-- Analyze existing user role distribution
SELECT 
    role,
    COUNT(*) as user_count,
    STRING_AGG(username, ', ') as usernames
FROM users 
WHERE role IS NOT NULL
GROUP BY role
ORDER BY user_count DESC;

-- Check for null or empty roles
SELECT COUNT(*) as users_without_role
FROM users 
WHERE role IS NULL OR TRIM(role) = '';

-- Verify users table structure
SELECT column_name, data_type, is_nullable
FROM information_schema.columns
WHERE table_name = 'users' AND column_name IN ('user_id', 'role', 'role_id');
```

### 3. Role Mapping Strategy

#### Legacy Role to New Role Mapping
Based on analysis in `docs/Users and Role management.md` section 5.2:

```sql
-- Create mapping function for role migration
CREATE OR REPLACE FUNCTION map_legacy_role(legacy_role VARCHAR) 
RETURNS INTEGER AS $$
DECLARE
    mapped_role_id INTEGER;
BEGIN
    -- Map legacy roles to new role_id
    CASE LOWER(TRIM(legacy_role))
        WHEN 'admin' THEN
            SELECT role_id INTO mapped_role_id FROM roles WHERE role_name = 'Administrator';
        WHEN 'administrator' THEN
            SELECT role_id INTO mapped_role_id FROM roles WHERE role_name = 'Administrator';
        WHEN 'manager' THEN
            SELECT role_id INTO mapped_role_id FROM roles WHERE role_name = 'Manager';
        WHEN 'user' THEN
            SELECT role_id INTO mapped_role_id FROM roles WHERE role_name = 'User';
        WHEN 'readonly' THEN
            SELECT role_id INTO mapped_role_id FROM roles WHERE role_name = 'ReadOnly';
        WHEN 'read-only' THEN
            SELECT role_id INTO mapped_role_id FROM roles WHERE role_name = 'ReadOnly';
        ELSE
            -- Default to User role for unknown roles
            SELECT role_id INTO mapped_role_id FROM roles WHERE role_name = 'User';
    END CASE;
    
    RETURN mapped_role_id;
END;
$$ LANGUAGE plpgsql;
```

### 4. Migration Process

#### Step 1: Validation and Backup
```sql
-- Validate prerequisite data exists
DO $$
BEGIN
    -- Check roles table populated
    IF (SELECT COUNT(*) FROM roles) < 4 THEN
        RAISE EXCEPTION 'Roles table not properly populated. Run Task 02 first.';
    END IF;
    
    -- Check users table exists and has data
    IF (SELECT COUNT(*) FROM users) = 0 THEN
        RAISE EXCEPTION 'No users found to migrate.';
    END IF;
    
    -- Check role_id column exists
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'users' AND column_name = 'role_id'
    ) THEN
        RAISE EXCEPTION 'role_id column not found. Run Task 01 first.';
    END IF;
END $$;
```

#### Step 2: Role Migration
```sql
-- Begin migration transaction
BEGIN;

-- Create migration log table for tracking
CREATE TEMP TABLE migration_log (
    user_id INTEGER,
    username VARCHAR(100),
    old_role VARCHAR(50),
    new_role_id INTEGER,
    new_role_name VARCHAR(50),
    migration_status VARCHAR(20),
    migration_timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Perform role migration
UPDATE users 
SET role_id = map_legacy_role(role)
WHERE role_id IS NULL;

-- Log migration results
INSERT INTO migration_log (user_id, username, old_role, new_role_id, new_role_name, migration_status)
SELECT 
    u.user_id,
    u.username,
    u.role as old_role,
    u.role_id as new_role_id,
    r.role_name as new_role_name,
    'SUCCESS' as migration_status
FROM users u
LEFT JOIN roles r ON u.role_id = r.role_id
WHERE u.role_id IS NOT NULL;

-- Handle any failed migrations
INSERT INTO migration_log (user_id, username, old_role, migration_status)
SELECT 
    user_id,
    username,
    role as old_role,
    'FAILED' as migration_status
FROM users 
WHERE role_id IS NULL;
```

#### Step 3: Data Validation
```sql
-- Validate migration completeness
SELECT 
    migration_status,
    COUNT(*) as user_count
FROM migration_log
GROUP BY migration_status;

-- Verify all users have valid role_id
SELECT COUNT(*) as users_without_role_id
FROM users 
WHERE role_id IS NULL;

-- Check role distribution after migration
SELECT 
    r.role_name,
    COUNT(u.user_id) as user_count,
    STRING_AGG(u.username, ', ') as usernames
FROM users u
JOIN roles r ON u.role_id = r.role_id
GROUP BY r.role_name
ORDER BY user_count DESC;
```

### 5. Post-Migration Cleanup

#### Make role_id Required
```sql
-- After successful migration, make role_id NOT NULL
ALTER TABLE users ALTER COLUMN role_id SET NOT NULL;

-- Add helpful comment
COMMENT ON COLUMN users.role_id IS 'Foreign key to roles table. Replaces legacy role varchar field.';

-- Drop the mapping function (no longer needed)
DROP FUNCTION IF EXISTS map_legacy_role(VARCHAR);
```

#### Optional Legacy Field Handling
```sql
-- Option 1: Keep legacy role field for reference (recommended initially)
COMMENT ON COLUMN users.role IS 'Legacy role field. Use role_id for new permission system.';

-- Option 2: Remove legacy role field (after full transition)
-- ALTER TABLE users DROP COLUMN role;
```

### 6. Migration Verification Queries

#### Comprehensive Validation
```sql
-- Verify migration integrity
WITH migration_summary AS (
    SELECT 
        COUNT(*) as total_users,
        COUNT(role_id) as users_with_role_id,
        COUNT(DISTINCT role_id) as unique_roles_used
    FROM users
)
SELECT 
    total_users,
    users_with_role_id,
    unique_roles_used,
    CASE 
        WHEN total_users = users_with_role_id THEN 'COMPLETE'
        ELSE 'INCOMPLETE'
    END as migration_status
FROM migration_summary;

-- Check for data consistency
SELECT 
    u.username,
    u.role as legacy_role,
    r.role_name as new_role,
    CASE 
        WHEN LOWER(u.role) = LOWER(r.role_name) THEN 'EXACT_MATCH'
        WHEN LOWER(u.role) = 'admin' AND r.role_name = 'Administrator' THEN 'MAPPED'
        WHEN LOWER(u.role) = 'user' AND r.role_name = 'User' THEN 'MAPPED'
        ELSE 'DIFFERENT'
    END as mapping_type
FROM users u
JOIN roles r ON u.role_id = r.role_id
ORDER BY mapping_type, u.username;
```

## Acceptance Criteria

### Migration Completeness
- [ ] All existing users have valid role_id assigned
- [ ] No users left with NULL role_id after migration
- [ ] All role mappings follow defined strategy
- [ ] Migration log shows 100% success rate

### Data Integrity
- [ ] All role_id values reference valid roles
- [ ] Foreign key constraints are satisfied
- [ ] No orphaned user records
- [ ] Legacy role data preserved for reference

### Mapping Accuracy
- [ ] 'Admin' users mapped to 'Administrator' role
- [ ] 'User' users mapped to 'User' role
- [ ] Unknown roles defaulted to 'User' role safely
- [ ] Mapping logic handles case variations

### System Stability
- [ ] Migration completed within single transaction
- [ ] Rollback capability verified and tested
- [ ] No impact on existing application functionality
- [ ] Database performance maintained

## Integration Points

### Existing System
- Preserves existing user authentication
- Maintains compatibility with UserManager service
- Extends users table without breaking changes
- Supports existing login and session management

### Future Tasks
- Enables Task 04 permission models to use role_id
- Provides foundation for Task 05 permission service
- Supports Task 08 UserMasterForm integration
- Establishes user context for all permission checks

## Testing Requirements

### Pre-Migration Testing
```sql
-- Test mapping function with sample data
SELECT 
    test_role,
    map_legacy_role(test_role) as mapped_role_id,
    r.role_name as mapped_role_name
FROM (VALUES 
    ('Admin'), ('admin'), ('ADMIN'),
    ('User'), ('user'), ('USER'),
    ('Manager'), ('manager'),
    ('Unknown'), (''), (NULL)
) AS test_data(test_role)
LEFT JOIN roles r ON r.role_id = map_legacy_role(test_role);
```

### Post-Migration Testing
```sql
-- Verify user login still works
SELECT user_id, username, role_id FROM users WHERE username = 'test_user';

-- Test permission system integration
SELECT u.username, r.role_name, COUNT(rp.perm_id) as permission_count
FROM users u
JOIN roles r ON u.role_id = r.role_id
LEFT JOIN role_perms rp ON r.role_id = rp.role_id
GROUP BY u.username, r.role_name
ORDER BY u.username;
```

### Rollback Testing
- Test transaction rollback scenarios
- Verify data restoration procedures
- Validate backup and recovery processes
- Confirm system stability after rollback

## Implementation Notes

### Safety Measures
- Use transactions for atomic operations
- Create comprehensive migration logs
- Preserve legacy data for reference
- Implement rollback procedures

### Error Handling
- Handle NULL and empty role values
- Provide defaults for unknown roles
- Log all migration decisions
- Validate foreign key constraints

### Performance Considerations
- Use efficient UPDATE queries
- Minimize table locks during migration
- Batch operations for large user sets
- Monitor migration progress

## Files to Create/Modify
- `Modules/Procedures/Permissions/03-User-Migration.sql` (NEW)
- `users` table (role_id column populated and made NOT NULL)

## Dependencies
- Task 01: RBAC Database Schema Setup (REQUIRED)
- Task 02: Default Data Population (REQUIRED)

## Estimated Effort
- 2-3 hours for migration script development
- Additional 2 hours for testing and validation
- 1 hour for rollback procedure verification
