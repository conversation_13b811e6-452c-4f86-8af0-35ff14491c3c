# Task 13: System Forms Permission Integration

## Objective
Integrate permission checks into system management forms (UserManagementListForm, DatabaseForm, ParametersForm, etc.) to enforce RBAC security for administrative and system configuration operations.

## Prerequisites
- Task 05 (Permission Service and Caching) completed
- Task 06 (Permission Helpers and Utilities) completed
- Task 07 (MainFrame Ribbon Integration) completed
- Task 12 (Business Forms Integration) completed for pattern reference

## Scope
Implement comprehensive permission integration for system forms:
1. UserManagementListForm permission integration
2. DatabaseForm and ParametersForm integration
3. RoleMasterForm and SQLQueryForm integration
4. System form helper utilities
5. Administrative operation restrictions

## Deliverables

### 1. UserManagementListForm Integration
**File**: `ListForms/UserManagementListForm.cs` (MODIFY EXISTING)

#### Core Permission Integration
```csharp
// Add to existing UserManagementListForm class

private bool _hasReadPermission;
private bool _hasNewPermission;
private bool _hasEditPermission;
private bool _hasDeletePermission;

private void UserManagementListForm_Load(object sender, EventArgs e)
{
    try
    {
        // Check form access permission
        if (!CheckFormAccess())
            return;

        // Load permissions for current user
        LoadUserPermissions();

        // Existing initialization code...
        InitializeForm();
        LoadUserData();

        // Apply permission-based restrictions
        ApplyPermissionRestrictions();

        // Existing code...
    }
    catch (Exception ex)
    {
        // Existing error handling...
    }
}

private bool CheckFormAccess()
{
    try
    {
        if (!UserManager.Instance.IsUserLoggedIn)
        {
            MessageBox.Show("Please log in to access this form.", "Authentication Required");
            this.Close();
            return false;
        }

        var currentUserId = UserManager.Instance.CurrentUser.UserId;
        
        if (!PermissionService.Instance.HasPermission(currentUserId, 
            PermissionConstants.FormNames.UserManagementListForm, 
            PermissionConstants.PermissionTypes.Read))
        {
            MessageBox.Show("Access denied to User Management.", "Permission Error");
            this.Close();
            return false;
        }

        return true;
    }
    catch (Exception ex)
    {
        Debug.WriteLine($"Error checking form access: {ex.Message}");
        MessageBox.Show("Unable to verify permissions. Access denied.", "Permission Error");
        this.Close();
        return false;
    }
}

private void LoadUserPermissions()
{
    try
    {
        var currentUserId = UserManager.Instance.CurrentUser.UserId;
        var formName = PermissionConstants.FormNames.UserManagementListForm;

        _hasReadPermission = PermissionService.Instance.HasPermission(currentUserId, formName, PermissionConstants.PermissionTypes.Read);
        _hasNewPermission = PermissionService.Instance.HasPermission(currentUserId, formName, PermissionConstants.PermissionTypes.New);
        _hasEditPermission = PermissionService.Instance.HasPermission(currentUserId, formName, PermissionConstants.PermissionTypes.Edit);
        _hasDeletePermission = PermissionService.Instance.HasPermission(currentUserId, formName, PermissionConstants.PermissionTypes.Delete);

        Debug.WriteLine($"UserManagementListForm permissions - Read: {_hasReadPermission}, New: {_hasNewPermission}, Edit: {_hasEditPermission}, Delete: {_hasDeletePermission}");
    }
    catch (Exception ex)
    {
        Debug.WriteLine($"Error loading user permissions: {ex.Message}");
        _hasReadPermission = _hasNewPermission = _hasEditPermission = _hasDeletePermission = false;
    }
}

private void ApplyPermissionRestrictions()
{
    try
    {
        // Update button states based on permissions
        UpdateButtonStatesWithPermissions();

        // Apply grid restrictions
        ApplyGridPermissionRestrictions();

        // Update context menu
        UpdateContextMenuPermissions();

        // Update status display
        UpdatePermissionStatusDisplay();
    }
    catch (Exception ex)
    {
        Debug.WriteLine($"Error applying permission restrictions: {ex.Message}");
    }
}

private void UpdateButtonStatesWithPermissions()
{
    try
    {
        // Update ribbon buttons
        if (BarButtonItemNew != null)
            BarButtonItemNew.Enabled = _hasNewPermission;

        if (BarButtonItemEdit != null)
            BarButtonItemEdit.Enabled = _hasEditPermission && HasSelectedUser();

        if (BarButtonItemDelete != null)
            BarButtonItemDelete.Enabled = _hasDeletePermission && HasSelectedUser();

        if (BarButtonItemPrint != null)
            BarButtonItemPrint.Enabled = _hasReadPermission && HasSelectedUser();

        // Update toolbar buttons if they exist
        if (btnNew != null)
            btnNew.Enabled = _hasNewPermission;

        if (btnEdit != null)
            btnEdit.Enabled = _hasEditPermission && HasSelectedUser();

        if (btnDelete != null)
            btnDelete.Enabled = _hasDeletePermission && HasSelectedUser();
    }
    catch (Exception ex)
    {
        Debug.WriteLine($"Error updating button states: {ex.Message}");
    }
}

private void ApplyGridPermissionRestrictions()
{
    try
    {
        if (viewUserList == null) return;

        // Configure grid based on permissions
        viewUserList.OptionsSelection.EnableAppearanceFocusedRow = _hasEditPermission || _hasDeletePermission;
        
        // Configure double-click behavior
        if (_hasEditPermission)
        {
            viewUserList.DoubleClick += ViewUserList_DoubleClick;
        }
        else
        {
            viewUserList.DoubleClick -= ViewUserList_DoubleClick;
        }
    }
    catch (Exception ex)
    {
        Debug.WriteLine($"Error applying grid permission restrictions: {ex.Message}");
    }
}

private void UpdateContextMenuPermissions()
{
    try
    {
        if (contextMenuUserList != null)
        {
            foreach (ToolStripItem item in contextMenuUserList.Items)
            {
                switch (item.Name)
                {
                    case "menuNewUser":
                        item.Enabled = _hasNewPermission;
                        break;
                    case "menuEditUser":
                        item.Enabled = _hasEditPermission && HasSelectedUser();
                        break;
                    case "menuDeleteUser":
                        item.Enabled = _hasDeletePermission && HasSelectedUser();
                        break;
                    case "menuViewUser":
                        item.Enabled = _hasReadPermission && HasSelectedUser();
                        break;
                }
            }
        }
    }
    catch (Exception ex)
    {
        Debug.WriteLine($"Error updating context menu permissions: {ex.Message}");
    }
}

private bool HasSelectedUser()
{
    try
    {
        return viewUserList?.FocusedRowHandle >= 0;
    }
    catch
    {
        return false;
    }
}

// Modify existing button event handlers
private void BarButtonItemNew_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
{
    try
    {
        if (!_hasNewPermission)
        {
            MessageBox.Show("You do not have permission to create new users.", 
                "Permission Denied", MessageBoxButtons.OK, MessageBoxIcon.Warning);
            return;
        }

        // Existing new user logic...
        OpenUserMasterForm(0); // 0 for new user
    }
    catch (Exception ex)
    {
        // Existing error handling...
    }
}

private void BarButtonItemEdit_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
{
    try
    {
        if (!_hasEditPermission)
        {
            MessageBox.Show("You do not have permission to edit users.", 
                "Permission Denied", MessageBoxButtons.OK, MessageBoxIcon.Warning);
            return;
        }

        if (!HasSelectedUser())
        {
            MessageBox.Show("Please select a user to edit.", 
                "No Selection", MessageBoxButtons.OK, MessageBoxIcon.Information);
            return;
        }

        // Existing edit user logic...
        var selectedUserId = GetSelectedUserId();
        OpenUserMasterForm(selectedUserId);
    }
    catch (Exception ex)
    {
        // Existing error handling...
    }
}

private void BarButtonItemDelete_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
{
    try
    {
        if (!_hasDeletePermission)
        {
            MessageBox.Show("You do not have permission to delete users.", 
                "Permission Denied", MessageBoxButtons.OK, MessageBoxIcon.Warning);
            return;
        }

        if (!HasSelectedUser())
        {
            MessageBox.Show("Please select a user to delete.", 
                "No Selection", MessageBoxButtons.OK, MessageBoxIcon.Information);
            return;
        }

        // Existing delete confirmation and logic...
        var result = MessageBox.Show("Are you sure you want to delete the selected user?", 
            "Confirm Delete", MessageBoxButtons.YesNo, MessageBoxIcon.Question);
        
        if (result == DialogResult.Yes)
        {
            DeleteSelectedUser();
        }
    }
    catch (Exception ex)
    {
        // Existing error handling...
    }
}

private void ViewUserList_SelectionChanged(object sender, DevExpress.XtraGrid.Views.Base.SelectionChangedEventArgs e)
{
    try
    {
        // Update button states when selection changes
        UpdateButtonStatesWithPermissions();
        UpdateContextMenuPermissions();
    }
    catch (Exception ex)
    {
        Debug.WriteLine($"Error handling selection change: {ex.Message}");
    }
}
```

### 2. System Forms Helper
**File**: `Modules/Helpers/Forms/SystemFormPermissionHelper.cs` (NEW)

#### Reusable System Form Helper
```csharp
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using DevExpress.XtraGrid.Views.Grid;
using ProManage.Modules.Services;
using ProManage.Modules.Helpers.Permissions;

namespace ProManage.Modules.Helpers.Forms
{
    public static class SystemFormPermissionHelper
    {
        public static bool CheckSystemFormAccess(Form form, string formName)
        {
            try
            {
                if (!UserManager.Instance.IsUserLoggedIn)
                {
                    MessageBox.Show("Please log in to access this form.", "Authentication Required");
                    form.Close();
                    return false;
                }

                var currentUserId = UserManager.Instance.CurrentUser.UserId;
                
                if (!PermissionService.Instance.HasPermission(currentUserId, formName, PermissionConstants.PermissionTypes.Read))
                {
                    var displayName = GetSystemFormDisplayName(formName);
                    MessageBox.Show($"Access denied to {displayName}.", "Permission Error");
                    form.Close();
                    return false;
                }

                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error checking system form access: {ex.Message}");
                MessageBox.Show("Unable to verify permissions. Access denied.", "Permission Error");
                form.Close();
                return false;
            }
        }

        public static SystemFormPermissions LoadSystemFormPermissions(string formName)
        {
            try
            {
                var currentUserId = UserManager.Instance.CurrentUser.UserId;

                return new SystemFormPermissions
                {
                    CanRead = PermissionService.Instance.HasPermission(currentUserId, formName, PermissionConstants.PermissionTypes.Read),
                    CanNew = PermissionService.Instance.HasPermission(currentUserId, formName, PermissionConstants.PermissionTypes.New),
                    CanEdit = PermissionService.Instance.HasPermission(currentUserId, formName, PermissionConstants.PermissionTypes.Edit),
                    CanDelete = PermissionService.Instance.HasPermission(currentUserId, formName, PermissionConstants.PermissionTypes.Delete),
                    FormName = formName
                };
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error loading system form permissions: {ex.Message}");
                return new SystemFormPermissions { FormName = formName }; // Default to no permissions
            }
        }

        public static void ApplySystemFormPermissions(Form form, SystemFormPermissions permissions)
        {
            try
            {
                // Update system-specific buttons
                UpdateSystemButtonPermissions(form, permissions);

                // Apply configuration restrictions for system forms
                ApplyConfigurationRestrictions(form, permissions);

                // Update administrative controls
                UpdateAdministrativeControls(form, permissions);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error applying system form permissions: {ex.Message}");
            }
        }

        public static void UpdateSystemButtonPermissions(Form form, SystemFormPermissions permissions)
        {
            var systemButtonMappings = new Dictionary<string, Func<SystemFormPermissions, bool>>
            {
                // Standard CRUD buttons
                { "btnNew", p => p.CanNew },
                { "btnAdd", p => p.CanNew },
                { "btnCreate", p => p.CanNew },
                { "btnSave", p => p.CanEdit },
                { "btnUpdate", p => p.CanEdit },
                { "btnEdit", p => p.CanEdit },
                { "btnDelete", p => p.CanDelete },
                { "btnRemove", p => p.CanDelete },
                
                // Ribbon buttons
                { "BarButtonItemNew", p => p.CanNew },
                { "BarButtonItemSave", p => p.CanEdit },
                { "BarButtonItemEdit", p => p.CanEdit },
                { "BarButtonItemDelete", p => p.CanDelete },
                
                // System-specific buttons
                { "btnTestConnection", p => p.CanEdit },
                { "btnBackupDatabase", p => p.CanEdit },
                { "btnRestoreDatabase", p => p.CanEdit },
                { "btnExecuteQuery", p => p.CanEdit },
                { "btnResetParameters", p => p.CanEdit }
            };

            foreach (var mapping in systemButtonMappings)
            {
                var control = FindControlByName(form, mapping.Key);
                if (control != null)
                {
                    control.Enabled = mapping.Value(permissions);
                }
            }
        }

        public static void ApplyConfigurationRestrictions(Form form, SystemFormPermissions permissions)
        {
            // For configuration forms like DatabaseForm and ParametersForm
            if (!permissions.CanEdit)
            {
                // Disable configuration controls
                var configControls = FindControlsOfType<TextEdit>(form)
                    .Concat(FindControlsOfType<ComboBoxEdit>(form).Cast<Control>())
                    .Concat(FindControlsOfType<CheckEdit>(form).Cast<Control>());

                foreach (var control in configControls)
                {
                    control.Enabled = false;
                }
            }
        }

        public static void UpdateAdministrativeControls(Form form, SystemFormPermissions permissions)
        {
            // Special handling for administrative operations
            var adminControls = new[]
            {
                "btnBackup", "btnRestore", "btnMaintenance", "btnCleanup",
                "btnSystemReset", "btnAdvancedSettings"
            };

            foreach (var controlName in adminControls)
            {
                var control = FindControlByName(form, controlName);
                if (control != null)
                {
                    // Administrative operations require both Edit and Delete permissions
                    control.Enabled = permissions.CanEdit && permissions.CanDelete;
                }
            }
        }

        public static void ShowSystemPermissionDeniedMessage(string operation, string formContext = "")
        {
            var message = string.IsNullOrEmpty(formContext) 
                ? $"You do not have permission to {operation}."
                : $"You do not have permission to {operation} in {formContext}.";
                
            MessageBox.Show(message, "Permission Denied", MessageBoxButtons.OK, MessageBoxIcon.Warning);
        }

        public static string GetSystemPermissionStatusText(SystemFormPermissions permissions)
        {
            var restrictions = new List<string>();
            
            if (!permissions.CanNew) restrictions.Add("Cannot create new items");
            if (!permissions.CanEdit) restrictions.Add("Configuration locked");
            if (!permissions.CanDelete) restrictions.Add("Cannot delete items");

            return restrictions.Count > 0 ? string.Join(" | ", restrictions) : "Full administrative access";
        }

        public static Color GetSystemPermissionStatusColor(SystemFormPermissions permissions)
        {
            if (!permissions.CanEdit) return Color.Red; // Configuration locked
            if (!permissions.CanNew || !permissions.CanDelete) return Color.Orange;
            return Color.Green;
        }

        private static string GetSystemFormDisplayName(string formName)
        {
            var displayNames = new Dictionary<string, string>
            {
                { PermissionConstants.FormNames.UserManagementListForm, "User Management" },
                { PermissionConstants.FormNames.UserMasterForm, "User Entry" },
                { PermissionConstants.FormNames.DatabaseForm, "Database Configuration" },
                { PermissionConstants.FormNames.ParametersForm, "System Parameters" },
                { PermissionConstants.FormNames.RoleMasterForm, "Role Management" },
                { PermissionConstants.FormNames.SQLQueryForm, "SQL Query Tool" },
                { PermissionConstants.FormNames.PermissionManagementForm, "Permission Management" }
            };

            return displayNames.TryGetValue(formName, out var displayName) ? displayName : formName;
        }

        private static Control FindControlByName(Control parent, string name)
        {
            if (parent.Name == name) return parent;

            foreach (Control child in parent.Controls)
            {
                var found = FindControlByName(child, name);
                if (found != null) return found;
            }

            return null;
        }

        private static List<T> FindControlsOfType<T>(Control parent) where T : Control
        {
            var controls = new List<T>();
            
            if (parent is T control)
                controls.Add(control);

            foreach (Control child in parent.Controls)
            {
                controls.AddRange(FindControlsOfType<T>(child));
            }

            return controls;
        }
    }

    public class SystemFormPermissions
    {
        public bool CanRead { get; set; }
        public bool CanNew { get; set; }
        public bool CanEdit { get; set; }
        public bool CanDelete { get; set; }
        public string FormName { get; set; }

        public SystemFormPermissions()
        {
            CanRead = CanNew = CanEdit = CanDelete = false;
        }

        public bool HasAdministrativeAccess => CanEdit && CanDelete;
        public bool IsReadOnly => CanRead && !CanEdit;
        public bool HasFullAccess => CanRead && CanNew && CanEdit && CanDelete;
    }
}
```

### 3. DatabaseForm Integration Template
**File**: `MainForms/DatabaseForm.cs` (MODIFY EXISTING - Template)

#### Database Configuration Security
```csharp
// Add to existing DatabaseForm class

private SystemFormPermissions _permissions;

private void DatabaseForm_Load(object sender, EventArgs e)
{
    try
    {
        // Check form access
        if (!SystemFormPermissionHelper.CheckSystemFormAccess(this, PermissionConstants.FormNames.DatabaseForm))
            return;

        // Load permissions
        _permissions = SystemFormPermissionHelper.LoadSystemFormPermissions(PermissionConstants.FormNames.DatabaseForm);
        
        // Apply permissions
        SystemFormPermissionHelper.ApplySystemFormPermissions(this, _permissions);
        
        // Existing initialization...
        InitializeForm();
        LoadDatabaseConfiguration();
        
        // Apply specific database form restrictions
        ApplyDatabaseFormRestrictions();
    }
    catch (Exception ex)
    {
        // Existing error handling...
    }
}

private void ApplyDatabaseFormRestrictions()
{
    try
    {
        // Database configuration requires edit permission
        if (!_permissions.CanEdit)
        {
            // Disable all configuration controls
            txtServerName.Enabled = false;
            txtDatabaseName.Enabled = false;
            txtUsername.Enabled = false;
            txtPassword.Enabled = false;
            chkIntegratedSecurity.Enabled = false;
        }

        // Test connection requires edit permission
        if (btnTestConnection != null)
            btnTestConnection.Enabled = _permissions.CanEdit;

        // Backup/Restore operations require administrative access
        if (btnBackupDatabase != null)
            btnBackupDatabase.Enabled = _permissions.HasAdministrativeAccess;

        if (btnRestoreDatabase != null)
            btnRestoreDatabase.Enabled = _permissions.HasAdministrativeAccess;
    }
    catch (Exception ex)
    {
        Debug.WriteLine($"Error applying database form restrictions: {ex.Message}");
    }
}

private void BtnSave_Click(object sender, EventArgs e)
{
    try
    {
        if (!_permissions.CanEdit)
        {
            SystemFormPermissionHelper.ShowSystemPermissionDeniedMessage("save database configuration");
            return;
        }

        // Existing save logic...
        SaveDatabaseConfiguration();
    }
    catch (Exception ex)
    {
        // Existing error handling...
    }
}

private void BtnTestConnection_Click(object sender, EventArgs e)
{
    try
    {
        if (!_permissions.CanEdit)
        {
            SystemFormPermissionHelper.ShowSystemPermissionDeniedMessage("test database connection");
            return;
        }

        // Existing test connection logic...
        TestDatabaseConnection();
    }
    catch (Exception ex)
    {
        // Existing error handling...
    }
}
```

### 4. System Forms Integration Guide
**File**: `Modules/Helpers/Forms/SystemFormIntegrationGuide.md` (NEW)

#### Integration Guide for System Forms
```markdown
# System Forms Permission Integration Guide

## Overview
This guide provides instructions for integrating permission checks into system management forms.

## Integration Pattern

### 1. Form Load Integration
```csharp
private void Form_Load(object sender, EventArgs e)
{
    try
    {
        if (!SystemFormPermissionHelper.CheckSystemFormAccess(this, "FormName"))
            return;

        var permissions = SystemFormPermissionHelper.LoadSystemFormPermissions("FormName");
        SystemFormPermissionHelper.ApplySystemFormPermissions(this, permissions);
        
        // Form-specific restrictions
        ApplyFormSpecificRestrictions(permissions);
    }
    catch (Exception ex)
    {
        // Error handling
    }
}
```

### 2. Administrative Operations
```csharp
private void AdminOperation_Click(object sender, EventArgs e)
{
    if (!_permissions.HasAdministrativeAccess)
    {
        SystemFormPermissionHelper.ShowSystemPermissionDeniedMessage("perform administrative operations");
        return;
    }
    
    // Operation logic
}
```

## Form-Specific Considerations

### DatabaseForm
- Configuration changes require Edit permission
- Backup/Restore require Administrative access (Edit + Delete)
- Connection testing requires Edit permission

### ParametersForm
- Parameter changes require Edit permission
- System reset requires Administrative access
- Parameter export requires Read permission

### SQLQueryForm
- Query execution requires Edit permission
- Schema modifications require Administrative access
- Query saving requires Edit permission

### RoleMasterForm
- Role creation requires New permission
- Role modification requires Edit permission
- Role deletion requires Delete permission

## Testing Checklist
- [ ] Form access denied without Read permission
- [ ] Configuration locked without Edit permission
- [ ] Administrative operations blocked without full access
- [ ] Clear permission status display
- [ ] Proper error messages for denied operations
```

## Acceptance Criteria

### UserManagementListForm Integration
- [ ] Form access controlled by Read permission
- [ ] New/Edit/Delete operations restricted by permissions
- [ ] Button states reflect user permissions
- [ ] Grid selection and context menu controlled
- [ ] Clear permission status display

### System Forms Helper
- [ ] Reusable helper for all system forms
- [ ] Consistent permission checking patterns
- [ ] Administrative operation controls
- [ ] Configuration restriction capabilities

### Security Enforcement
- [ ] No bypass of permission checks possible
- [ ] Administrative operations properly restricted
- [ ] Configuration changes controlled
- [ ] Audit logging of permission violations

### User Experience
- [ ] Clear permission denied messages
- [ ] Intuitive administrative controls
- [ ] Visual indicators for restrictions
- [ ] Consistent with system form patterns

## Integration Points

### Existing System
- Uses established system form patterns
- Integrates with existing administrative controls
- Compatible with current configuration management
- Follows ProManage security conventions

### Future Tasks
- Completes form-level permission integration
- Establishes comprehensive system security
- Supports administrative access control
- Enables secure system management

## Testing Requirements

### Security Testing
```csharp
[Test]
public void UserManagementListForm_NoEditPermission_DisablesEditButton()
{
    // Test that edit operations are disabled without permission
}

[Test]
public void DatabaseForm_NoAdminAccess_DisablesBackupRestore()
{
    // Test that administrative operations require full access
}
```

### Administrative Testing
- Test configuration restriction enforcement
- Validate administrative operation controls
- Test with different administrative roles
- Verify system security boundaries

### Integration Testing
- Test with existing system form functionality
- Validate helper integration across forms
- Test performance impact of permission checks
- Verify compatibility with administrative workflows

## Implementation Notes

### Security Considerations
- Administrative operations require elevated permissions
- Configuration changes properly controlled
- System-level operations audited
- No unauthorized system access possible

### Performance Optimization
- Efficient permission loading for system forms
- Cached administrative permissions
- Minimal impact on system operations
- Optimized for administrative workflows

### Maintainability
- Consistent system form patterns
- Reusable administrative controls
- Clear separation of system permissions
- Extensible for future system forms

## Files to Create/Modify

### Modified Files (2+ files)
- `ListForms/UserManagementListForm.cs` (MODIFY - add permission integration)
- `MainForms/DatabaseForm.cs` (MODIFY - add permission integration)
- Additional system forms as identified

### New Files (2 files)
- `Modules/Helpers/Forms/SystemFormPermissionHelper.cs`
- `Modules/Helpers/Forms/SystemFormIntegrationGuide.md`

## Dependencies
- Task 05: Permission Service and Caching (REQUIRED)
- Task 06: Permission Helpers and Utilities (REQUIRED)
- Task 07: MainFrame Ribbon Integration (REQUIRED)
- Task 12: Business Forms Integration (REQUIRED for patterns)

## Estimated Effort
- 6-8 hours for UserManagementListForm integration
- 4-6 hours for system forms helper implementation
- 4-6 hours for DatabaseForm and other system forms
- 2-4 hours for integration guide and documentation
- 4-6 hours for testing and validation
