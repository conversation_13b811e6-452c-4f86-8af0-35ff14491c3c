-- =====================================================
-- ProManage RBAC System - Remove Unnecessary Tables
-- =====================================================
--
-- This script removes the unnecessary 'forms' and 'permission_types' 
-- tables that were created in the original 5-table approach.
-- 
-- The corrected 3-table approach uses:
-- 1. roles (required)
-- 2. role_perms (required) 
-- 3. user_perms (required)
--
-- Form names and permission types are hardcoded in the application.
--
-- =====================================================

-- Start transaction for atomic operation
BEGIN;

-- Set error handling
\set ON_ERROR_STOP on

-- =====================================================
-- SECTION 1: BACKUP DATA (if needed)
-- =====================================================

-- Check if tables exist and have data
DO $$
DECLARE
    forms_count INTEGER := 0;
    permission_types_count INTEGER := 0;
BEGIN
    -- Check forms table
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'forms') THEN
        SELECT COUNT(*) INTO forms_count FROM forms;
        RAISE NOTICE 'forms table exists with % records', forms_count;
    ELSE
        RAISE NOTICE 'forms table does not exist - nothing to remove';
    END IF;
    
    -- Check permission_types table
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'permission_types') THEN
        SELECT COUNT(*) INTO permission_types_count FROM permission_types;
        RAISE NOTICE 'permission_types table exists with % records', permission_types_count;
    ELSE
        RAISE NOTICE 'permission_types table does not exist - nothing to remove';
    END IF;
    
    IF forms_count > 0 OR permission_types_count > 0 THEN
        RAISE NOTICE 'These tables will be dropped as they are not needed in the 3-table approach';
    END IF;
END $$;

-- =====================================================
-- SECTION 2: REMOVE UNNECESSARY TABLES
-- =====================================================

-- Drop forms table if it exists
DROP TABLE IF EXISTS forms CASCADE;
RAISE NOTICE 'Dropped forms table (not required in 3-table approach)';

-- Drop permission_types table if it exists  
DROP TABLE IF EXISTS permission_types CASCADE;
RAISE NOTICE 'Dropped permission_types table (not required in 3-table approach)';

-- =====================================================
-- SECTION 3: VERIFICATION
-- =====================================================

-- Verify the 3 required tables still exist
DO $$
DECLARE
    required_tables TEXT[] := ARRAY['roles', 'role_perms', 'user_perms'];
    table_name TEXT;
    table_count INTEGER;
BEGIN
    RAISE NOTICE 'Verifying required tables still exist...';
    
    FOREACH table_name IN ARRAY required_tables LOOP
        SELECT COUNT(*) INTO table_count
        FROM information_schema.tables
        WHERE table_name = table_name;
        
        IF table_count = 0 THEN
            RAISE EXCEPTION 'Required table % is missing!', table_name;
        ELSE
            RAISE NOTICE 'Required table % exists ✓', table_name;
        END IF;
    END LOOP;
END $$;

-- Verify unnecessary tables are gone
DO $$
DECLARE
    unnecessary_tables TEXT[] := ARRAY['forms', 'permission_types'];
    table_name TEXT;
    table_count INTEGER;
BEGIN
    RAISE NOTICE 'Verifying unnecessary tables are removed...';
    
    FOREACH table_name IN ARRAY unnecessary_tables LOOP
        SELECT COUNT(*) INTO table_count
        FROM information_schema.tables
        WHERE table_name = table_name;
        
        IF table_count > 0 THEN
            RAISE WARNING 'Table % still exists!', table_name;
        ELSE
            RAISE NOTICE 'Table % successfully removed ✓', table_name;
        END IF;
    END LOOP;
END $$;

-- Final summary
SELECT
    'RBAC Cleanup Summary' as component,
    (SELECT COUNT(*) FROM roles) as roles_count,
    (SELECT COUNT(*) FROM role_perms) as role_permissions_count,
    (SELECT COUNT(*) FROM user_perms) as user_permissions_count,
    (SELECT COUNT(*) FROM users WHERE role_id IS NOT NULL) as migrated_users_count;

-- =====================================================
-- SECTION 4: COMPLETION
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE '=====================================================';
    RAISE NOTICE 'RBAC Cleanup Completed Successfully!';
    RAISE NOTICE '=====================================================';
    RAISE NOTICE 'Removed unnecessary tables:';
    RAISE NOTICE '- forms (not required - form names hardcoded in app)';
    RAISE NOTICE '- permission_types (not required - permission types hardcoded in app)';
    RAISE NOTICE '';
    RAISE NOTICE 'Remaining required tables:';
    RAISE NOTICE '- roles (role definitions)';
    RAISE NOTICE '- role_perms (role-based permissions)';
    RAISE NOTICE '- user_perms (user overrides and global restrictions)';
    RAISE NOTICE '=====================================================';
END $$;

-- Commit the transaction
COMMIT;

-- Final success message
\echo 'RBAC cleanup completed successfully!'
\echo 'Database now uses the simplified 3-table approach.'
