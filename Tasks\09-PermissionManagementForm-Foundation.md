# Task 09: PermissionManagementForm Foundation and Structure

## Objective
Create the foundational PermissionManagementForm with a three-tab structure for comprehensive permission management, following ProManage's established form patterns and MDI architecture.

## Prerequisites
- Task 05 (Permission Service and Caching) completed
- Task 06 (Permission Helpers and Utilities) completed
- Understanding of ProManage form patterns and MDI architecture
- DevExpress controls and styling knowledge

## Scope
Create the complete form foundation:
1. Main form structure with three-tab design
2. Navigation and form lifecycle management
3. Permission checks and security integration
4. Base infrastructure for tab implementations
5. Integration with existing ProManage form patterns

## Deliverables

### 1. PermissionManagementForm Structure
**File**: `Forms/MainForms/PermissionManagementForm.cs`

#### Main Form Class
```csharp
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using DevExpress.XtraTab;
using ProManage.Modules.Services;
using ProManage.Modules.Helpers.Permissions;
using ProManage.Modules.Models.Permissions;

namespace ProManage.Forms.MainForms
{
    public partial class PermissionManagementForm : DevExpress.XtraEditors.XtraForm
    {
        private int _targetUserId;
        private bool _isInitialized;
        private bool _hasUnsavedChanges;

        public PermissionManagementForm(int userId = 0)
        {
            InitializeComponent();
            _targetUserId = userId;
            _isInitialized = false;
            _hasUnsavedChanges = false;
        }

        private void PermissionManagementForm_Load(object sender, EventArgs e)
        {
            try
            {
                // Check permission to access this form
                if (!CheckFormAccess())
                    return;

                InitializeForm();
                LoadInitialData();
                _isInitialized = true;
            }
            catch (Exception ex)
            {
                HandleError(ex, "Error loading Permission Management form");
            }
        }

        private bool CheckFormAccess()
        {
            try
            {
                var currentUserId = UserManager.Instance.CurrentUser.UserId;
                
                if (!PermissionService.Instance.HasPermission(currentUserId, 
                    PermissionConstants.FormNames.PermissionManagementForm, 
                    PermissionConstants.PermissionTypes.Read))
                {
                    MessageBox.Show("Access denied to Permission Management.", 
                        "Permission Error", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    this.Close();
                    return false;
                }

                return true;
            }
            catch (Exception ex)
            {
                HandleError(ex, "Error checking form access");
                this.Close();
                return false;
            }
        }

        private void InitializeForm()
        {
            this.Text = "Permission Management";
            this.WindowState = FormWindowState.Maximized;
            this.StartPosition = FormStartPosition.CenterParent;

            // Configure tab control
            ConfigureTabControl();

            // Set initial tab based on target user
            if (_targetUserId > 0)
            {
                tabControl.SelectedTabPageIndex = 1; // User Overrides tab
            }
            else
            {
                tabControl.SelectedTabPageIndex = 0; // Role Permissions tab
            }

            // Configure form buttons
            ConfigureFormButtons();
        }

        private void ConfigureTabControl()
        {
            tabControl.TabPages.Clear();

            // Tab 1: Role Permissions Matrix
            var tabRolePermissions = new XtraTabPage();
            tabRolePermissions.Name = "tabRolePermissions";
            tabRolePermissions.Text = "Role Permissions";
            tabRolePermissions.Tag = "role_permissions";
            tabControl.TabPages.Add(tabRolePermissions);

            // Tab 2: User Permission Overrides
            var tabUserOverrides = new XtraTabPage();
            tabUserOverrides.Name = "tabUserOverrides";
            tabUserOverrides.Text = "User Overrides";
            tabUserOverrides.Tag = "user_overrides";
            tabControl.TabPages.Add(tabUserOverrides);

            // Tab 3: Global Restrictions
            var tabGlobalRestrictions = new XtraTabPage();
            tabGlobalRestrictions.Name = "tabGlobalRestrictions";
            tabGlobalRestrictions.Text = "Global Restrictions";
            tabGlobalRestrictions.Tag = "global_restrictions";
            tabControl.TabPages.Add(tabGlobalRestrictions);

            // Subscribe to tab change events
            tabControl.SelectedPageChanged += TabControl_SelectedPageChanged;
        }

        private void ConfigureFormButtons()
        {
            // Configure Save button
            btnSave.Text = "Save Changes";
            btnSave.Enabled = false;
            btnSave.Click += BtnSave_Click;

            // Configure Cancel button
            btnCancel.Text = "Cancel";
            btnCancel.Click += BtnCancel_Click;

            // Configure Refresh button
            btnRefresh.Text = "Refresh";
            btnRefresh.Click += BtnRefresh_Click;

            // Configure Close button
            btnClose.Text = "Close";
            btnClose.Click += BtnClose_Click;
        }

        private void LoadInitialData()
        {
            try
            {
                // Load data for all tabs
                LoadRolePermissionsData();
                LoadUserOverridesData();
                LoadGlobalRestrictionsData();

                // Select target user if specified
                if (_targetUserId > 0)
                {
                    SelectTargetUser(_targetUserId);
                }
            }
            catch (Exception ex)
            {
                HandleError(ex, "Error loading initial data");
            }
        }

        // Placeholder methods for tab-specific data loading
        private void LoadRolePermissionsData()
        {
            // Implementation in Task 10
        }

        private void LoadUserOverridesData()
        {
            // Implementation in Task 10
        }

        private void LoadGlobalRestrictionsData()
        {
            // Implementation in Task 11
        }

        private void SelectTargetUser(int userId)
        {
            // Implementation in Task 10
        }
    }
}
```

### 2. PermissionManagementForm Designer
**File**: `Forms/MainForms/PermissionManagementForm.Designer.cs`

#### Designer Implementation
```csharp
partial class PermissionManagementForm
{
    private System.ComponentModel.IContainer components = null;
    private DevExpress.XtraTab.XtraTabControl tabControl;
    private DevExpress.XtraEditors.SimpleButton btnSave;
    private DevExpress.XtraEditors.SimpleButton btnCancel;
    private DevExpress.XtraEditors.SimpleButton btnRefresh;
    private DevExpress.XtraEditors.SimpleButton btnClose;
    private DevExpress.XtraEditors.PanelControl panelButtons;
    private DevExpress.XtraEditors.LabelControl lblStatus;

    protected override void Dispose(bool disposing)
    {
        if (disposing && (components != null))
        {
            components.Dispose();
        }
        base.Dispose(disposing);
    }

    private void InitializeComponent()
    {
        this.tabControl = new DevExpress.XtraTab.XtraTabControl();
        this.btnSave = new DevExpress.XtraEditors.SimpleButton();
        this.btnCancel = new DevExpress.XtraEditors.SimpleButton();
        this.btnRefresh = new DevExpress.XtraEditors.SimpleButton();
        this.btnClose = new DevExpress.XtraEditors.SimpleButton();
        this.panelButtons = new DevExpress.XtraEditors.PanelControl();
        this.lblStatus = new DevExpress.XtraEditors.LabelControl();
        ((System.ComponentModel.ISupportInitialize)(this.tabControl)).BeginInit();
        ((System.ComponentModel.ISupportInitialize)(this.panelButtons)).BeginInit();
        this.panelButtons.SuspendLayout();
        this.SuspendLayout();

        // tabControl
        this.tabControl.Dock = System.Windows.Forms.DockStyle.Fill;
        this.tabControl.Location = new System.Drawing.Point(0, 0);
        this.tabControl.Name = "tabControl";
        this.tabControl.Size = new System.Drawing.Size(1200, 700);
        this.tabControl.TabIndex = 0;

        // panelButtons
        this.panelButtons.Controls.Add(this.btnSave);
        this.panelButtons.Controls.Add(this.btnCancel);
        this.panelButtons.Controls.Add(this.btnRefresh);
        this.panelButtons.Controls.Add(this.btnClose);
        this.panelButtons.Controls.Add(this.lblStatus);
        this.panelButtons.Dock = System.Windows.Forms.DockStyle.Bottom;
        this.panelButtons.Location = new System.Drawing.Point(0, 700);
        this.panelButtons.Name = "panelButtons";
        this.panelButtons.Size = new System.Drawing.Size(1200, 60);
        this.panelButtons.TabIndex = 1;

        // btnSave
        this.btnSave.Location = new System.Drawing.Point(12, 15);
        this.btnSave.Name = "btnSave";
        this.btnSave.Size = new System.Drawing.Size(100, 30);
        this.btnSave.TabIndex = 0;
        this.btnSave.Text = "Save Changes";

        // btnCancel
        this.btnCancel.Location = new System.Drawing.Point(118, 15);
        this.btnCancel.Name = "btnCancel";
        this.btnCancel.Size = new System.Drawing.Size(75, 30);
        this.btnCancel.TabIndex = 1;
        this.btnCancel.Text = "Cancel";

        // btnRefresh
        this.btnRefresh.Location = new System.Drawing.Point(199, 15);
        this.btnRefresh.Name = "btnRefresh";
        this.btnRefresh.Size = new System.Drawing.Size(75, 30);
        this.btnRefresh.TabIndex = 2;
        this.btnRefresh.Text = "Refresh";

        // btnClose
        this.btnClose.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
        this.btnClose.Location = new System.Drawing.Point(1113, 15);
        this.btnClose.Name = "btnClose";
        this.btnClose.Size = new System.Drawing.Size(75, 30);
        this.btnClose.TabIndex = 3;
        this.btnClose.Text = "Close";

        // lblStatus
        this.lblStatus.Location = new System.Drawing.Point(300, 22);
        this.lblStatus.Name = "lblStatus";
        this.lblStatus.Size = new System.Drawing.Size(29, 16);
        this.lblStatus.TabIndex = 4;
        this.lblStatus.Text = "Ready";

        // PermissionManagementForm
        this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 16F);
        this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
        this.ClientSize = new System.Drawing.Size(1200, 760);
        this.Controls.Add(this.tabControl);
        this.Controls.Add(this.panelButtons);
        this.Name = "PermissionManagementForm";
        this.Text = "Permission Management";
        this.Load += new System.EventHandler(this.PermissionManagementForm_Load);
        this.FormClosing += new System.Windows.Forms.FormClosingEventHandler(this.PermissionManagementForm_FormClosing);
        ((System.ComponentModel.ISupportInitialize)(this.tabControl)).EndInit();
        ((System.ComponentModel.ISupportInitialize)(this.panelButtons)).EndInit();
        this.panelButtons.ResumeLayout(false);
        this.panelButtons.PerformLayout();
        this.ResumeLayout(false);
    }
}
```

### 3. Form Event Handlers and Navigation
**File**: `Forms/MainForms/PermissionManagementForm.cs` (CONTINUED)

#### Event Handlers
```csharp
// Add to PermissionManagementForm class

private void TabControl_SelectedPageChanged(object sender, DevExpress.XtraTab.TabPageChangedEventArgs e)
{
    try
    {
        if (!_isInitialized) return;

        // Check for unsaved changes before switching tabs
        if (_hasUnsavedChanges)
        {
            var result = MessageBox.Show(
                "You have unsaved changes. Do you want to save them before switching tabs?",
                "Unsaved Changes",
                MessageBoxButtons.YesNoCancel,
                MessageBoxIcon.Question);

            if (result == DialogResult.Cancel)
            {
                // Prevent tab change
                tabControl.SelectedTabPage = e.PrevPage;
                return;
            }
            else if (result == DialogResult.Yes)
            {
                if (!SaveCurrentTabChanges())
                {
                    tabControl.SelectedTabPage = e.PrevPage;
                    return;
                }
            }
            else
            {
                // Discard changes
                DiscardCurrentTabChanges();
            }
        }

        // Load data for the new tab
        LoadTabData(e.Page);
        UpdateButtonStates();
    }
    catch (Exception ex)
    {
        HandleError(ex, "Error switching tabs");
    }
}

private void BtnSave_Click(object sender, EventArgs e)
{
    try
    {
        if (SaveCurrentTabChanges())
        {
            _hasUnsavedChanges = false;
            UpdateButtonStates();
            UpdateStatus("Changes saved successfully");
        }
    }
    catch (Exception ex)
    {
        HandleError(ex, "Error saving changes");
    }
}

private void BtnCancel_Click(object sender, EventArgs e)
{
    try
    {
        if (_hasUnsavedChanges)
        {
            var result = MessageBox.Show(
                "Are you sure you want to discard your changes?",
                "Discard Changes",
                MessageBoxButtons.YesNo,
                MessageBoxIcon.Question);

            if (result == DialogResult.Yes)
            {
                DiscardCurrentTabChanges();
                _hasUnsavedChanges = false;
                UpdateButtonStates();
                UpdateStatus("Changes discarded");
            }
        }
    }
    catch (Exception ex)
    {
        HandleError(ex, "Error canceling changes");
    }
}

private void BtnRefresh_Click(object sender, EventArgs e)
{
    try
    {
        if (_hasUnsavedChanges)
        {
            var result = MessageBox.Show(
                "Refreshing will discard unsaved changes. Continue?",
                "Refresh Data",
                MessageBoxButtons.YesNo,
                MessageBoxIcon.Question);

            if (result != DialogResult.Yes)
                return;
        }

        LoadTabData(tabControl.SelectedTabPage);
        _hasUnsavedChanges = false;
        UpdateButtonStates();
        UpdateStatus("Data refreshed");
    }
    catch (Exception ex)
    {
        HandleError(ex, "Error refreshing data");
    }
}

private void BtnClose_Click(object sender, EventArgs e)
{
    this.Close();
}

private void PermissionManagementForm_FormClosing(object sender, FormClosingEventArgs e)
{
    try
    {
        if (_hasUnsavedChanges)
        {
            var result = MessageBox.Show(
                "You have unsaved changes. Do you want to save them before closing?",
                "Unsaved Changes",
                MessageBoxButtons.YesNoCancel,
                MessageBoxIcon.Question);

            if (result == DialogResult.Cancel)
            {
                e.Cancel = true;
                return;
            }
            else if (result == DialogResult.Yes)
            {
                if (!SaveCurrentTabChanges())
                {
                    e.Cancel = true;
                    return;
                }
            }
        }
    }
    catch (Exception ex)
    {
        HandleError(ex, "Error closing form");
        e.Cancel = true;
    }
}
```

#### Helper Methods
```csharp
private void LoadTabData(XtraTabPage tabPage)
{
    try
    {
        UpdateStatus("Loading data...");

        switch (tabPage.Tag?.ToString())
        {
            case "role_permissions":
                LoadRolePermissionsData();
                break;
            case "user_overrides":
                LoadUserOverridesData();
                break;
            case "global_restrictions":
                LoadGlobalRestrictionsData();
                break;
        }

        UpdateStatus("Ready");
    }
    catch (Exception ex)
    {
        UpdateStatus("Error loading data");
        throw;
    }
}

private bool SaveCurrentTabChanges()
{
    try
    {
        UpdateStatus("Saving changes...");

        var currentTab = tabControl.SelectedTabPage;
        bool success = false;

        switch (currentTab.Tag?.ToString())
        {
            case "role_permissions":
                success = SaveRolePermissionsChanges();
                break;
            case "user_overrides":
                success = SaveUserOverridesChanges();
                break;
            case "global_restrictions":
                success = SaveGlobalRestrictionsChanges();
                break;
        }

        if (success)
        {
            UpdateStatus("Changes saved");
            return true;
        }
        else
        {
            UpdateStatus("Save failed");
            return false;
        }
    }
    catch (Exception ex)
    {
        UpdateStatus("Save error");
        throw;
    }
}

private void DiscardCurrentTabChanges()
{
    try
    {
        var currentTab = tabControl.SelectedTabPage;

        switch (currentTab.Tag?.ToString())
        {
            case "role_permissions":
                LoadRolePermissionsData();
                break;
            case "user_overrides":
                LoadUserOverridesData();
                break;
            case "global_restrictions":
                LoadGlobalRestrictionsData();
                break;
        }
    }
    catch (Exception ex)
    {
        HandleError(ex, "Error discarding changes");
    }
}

// Placeholder methods for tab-specific operations (implemented in Tasks 10-11)
private bool SaveRolePermissionsChanges() { return true; }
private bool SaveUserOverridesChanges() { return true; }
private bool SaveGlobalRestrictionsChanges() { return true; }

private void UpdateButtonStates()
{
    btnSave.Enabled = _hasUnsavedChanges;
    btnCancel.Enabled = _hasUnsavedChanges;
}

private void UpdateStatus(string message)
{
    lblStatus.Text = message;
    Application.DoEvents();
}

private void HandleError(Exception ex, string context)
{
    var message = $"{context}: {ex.Message}";
    Debug.WriteLine(message);
    MessageBox.Show(message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
    UpdateStatus("Error occurred");
}

public void MarkAsChanged()
{
    _hasUnsavedChanges = true;
    UpdateButtonStates();
}
```

## Acceptance Criteria

### Form Structure
- [ ] Three-tab structure implemented correctly
- [ ] Proper MDI child form behavior
- [ ] DevExpress styling consistent with ProManage
- [ ] Form resizing and layout working properly

### Navigation and Lifecycle
- [ ] Tab switching with unsaved changes handling
- [ ] Form closing with unsaved changes protection
- [ ] Proper initialization and data loading
- [ ] Error handling for all operations

### Permission Integration
- [ ] Form access controlled by permissions
- [ ] Permission checks for management operations
- [ ] Integration with existing UserManager service
- [ ] Proper error messages for access denied

### User Experience
- [ ] Clear status messages and feedback
- [ ] Intuitive button states and enabling
- [ ] Consistent with ProManage UI patterns
- [ ] Responsive form performance

## Integration Points

### Existing System
- Uses ProManage MDI architecture patterns
- Integrates with UserManager service
- Follows existing form lifecycle patterns
- Compatible with DevExpress styling

### Future Tasks
- Provides foundation for Task 10 (Role and User Permission tabs)
- Enables Task 11 (Global Restrictions tab)
- Supports integration with UserMasterForm
- Establishes pattern for permission management UI

## Testing Requirements

### Form Testing
```csharp
[Test]
public void PermissionManagementForm_Load_ChecksPermissions()
{
    // Test that form checks permissions on load
}

[Test]
public void TabSwitching_WithUnsavedChanges_PromptsUser()
{
    // Test unsaved changes handling during tab switching
}
```

### Integration Testing
- Test form opening from UserMasterForm
- Validate permission checks work correctly
- Test form lifecycle and cleanup
- Verify MDI integration

### User Experience Testing
- Test tab navigation and data loading
- Validate error handling scenarios
- Test form closing with unsaved changes
- Verify status messages and feedback

## Implementation Notes

### Performance Considerations
- Lazy loading of tab data
- Efficient form initialization
- Minimal impact on application startup
- Optimized DevExpress control usage

### Security Considerations
- Comprehensive permission checks
- Secure handling of permission changes
- Audit logging of management operations
- Protection against unauthorized access

### Maintainability
- Clear separation of tab-specific logic
- Reusable error handling patterns
- Extensible design for future tabs
- Consistent coding patterns

## Files to Create/Modify

### New Files (3 files)
- `Forms/MainForms/PermissionManagementForm.cs`
- `Forms/MainForms/PermissionManagementForm.Designer.cs`
- `Forms/MainForms/PermissionManagementForm.resx`

## Dependencies
- Task 05: Permission Service and Caching (REQUIRED)
- Task 06: Permission Helpers and Utilities (REQUIRED)
- DevExpress controls and licensing
- Existing ProManage form patterns

## Estimated Effort
- 4-6 hours for form structure and designer
- 4-6 hours for navigation and lifecycle management
- 2-4 hours for permission integration
- 2-3 hours for testing and refinement
