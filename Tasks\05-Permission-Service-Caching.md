# Task 05: Permission Service and Caching System

## Objective
Implement the core PermissionService as a singleton service with intelligent caching, following ProManage's established service patterns (similar to ParameterCacheService and UserManager).

## Prerequisites
- Task 04 (Permission Models and Repository) completed
- All permission models and repository classes implemented
- Database contains test data for validation
- Understanding of existing UserManager and ParameterCacheService patterns

## Scope
Create a high-performance permission service with:
1. Singleton service pattern following ProManage conventions
2. Intelligent caching system for fast permission lookups
3. Integration with existing UserManager service
4. Cache invalidation and refresh mechanisms
5. Performance monitoring and optimization

## Deliverables

### 1. Core Permission Service
**File**: `Modules/Services/PermissionService.cs`

#### Service Structure
```csharp
public sealed class PermissionService
{
    private static readonly Lazy<PermissionService> _instance = 
        new Lazy<PermissionService>(() => new PermissionService());
    
    public static PermissionService Instance => _instance.Value;
    
    private readonly PermissionCacheService _cache;
    private readonly object _lockObject = new object();
    
    private PermissionService()
    {
        _cache = new PermissionCacheService();
        InitializeService();
    }
}
```

#### Core Methods
```csharp
// Primary permission checking methods
public bool HasPermission(int userId, string formName, string permissionType);
public bool HasPermission(string formName, string permissionType); // Uses current user
public Dictionary<string, Dictionary<string, bool>> GetUserPermissions(int userId);
public List<string> GetVisibleForms(int userId);

// Cache management
public void RefreshUserPermissions(int userId);
public void RefreshAllPermissions();
public void ClearCache();

// Permission management
public bool SetRolePermission(int roleId, string formName, string permissionType, bool isGranted);
public bool SetUserPermission(int userId, string formName, string permissionType, bool isGranted);
public bool SetUserManagementPermission(int userId, string permissionType, bool isGranted);

// Service status and diagnostics
public bool IsServiceReady { get; }
public PermissionServiceStatus GetServiceStatus();
public PermissionCacheStatistics GetCacheStatistics();
```

### 2. Permission Cache Service
**File**: `Modules/Services/PermissionCacheService.cs`

#### Cache Structure
```csharp
public class PermissionCacheService
{
    private readonly ConcurrentDictionary<int, UserPermissionCache> _userPermissions;
    private readonly ConcurrentDictionary<string, List<string>> _formVisibilityCache;
    private readonly Timer _cacheRefreshTimer;
    private readonly object _cacheLock = new object();
    
    public class UserPermissionCache
    {
        public int UserId { get; set; }
        public Dictionary<string, Dictionary<string, bool>> Permissions { get; set; }
        public List<string> VisibleForms { get; set; }
        public DateTime LastUpdated { get; set; }
        public bool IsValid => DateTime.Now - LastUpdated < TimeSpan.FromMinutes(30);
    }
}
```

#### Cache Methods
```csharp
// Cache operations
public bool GetCachedPermission(int userId, string formName, string permissionType);
public void LoadUserPermissions(int userId);
public void InvalidateUserCache(int userId);
public void InvalidateAllCache();

// Cache statistics
public int CachedUserCount { get; }
public double CacheHitRatio { get; }
public TimeSpan AverageLoadTime { get; }
public long TotalCacheHits { get; }
public long TotalCacheMisses { get; }
```

### 3. Permission Service Integration
**File**: `Modules/Services/PermissionServiceIntegration.cs`

#### UserManager Integration
```csharp
public static class PermissionServiceIntegration
{
    public static void InitializeWithUserManager()
    {
        // Subscribe to user login/logout events
        UserManager.Instance.UserLoggedIn += OnUserLoggedIn;
        UserManager.Instance.UserLoggedOut += OnUserLoggedOut;
        UserManager.Instance.UserChanged += OnUserChanged;
    }
    
    private static void OnUserLoggedIn(object sender, UserEventArgs e)
    {
        // Pre-load permissions for logged-in user
        PermissionService.Instance.RefreshUserPermissions(e.User.UserId);
    }
    
    private static void OnUserLoggedOut(object sender, UserEventArgs e)
    {
        // Clear user permissions from cache
        PermissionService.Instance.ClearUserCache(e.User.UserId);
    }
}
```

### 4. Permission Constants and Enums
**File**: `Modules/Helpers/Permissions/PermissionConstants.cs`

#### Constants Definition
```csharp
public static class PermissionConstants
{
    // Permission Types
    public static class PermissionTypes
    {
        public const string Read = "Read";
        public const string New = "New";
        public const string Edit = "Edit";
        public const string Delete = "Delete";
    }
    
    // Form Names
    public static class FormNames
    {
        public const string EstimateForm = "EstimateForm";
        public const string UserMasterForm = "UserMasterForm";
        public const string UserManagementListForm = "UserManagementListForm";
        public const string DatabaseForm = "DatabaseForm";
        public const string ParametersForm = "ParametersForm";
        public const string RoleMasterForm = "RoleMasterForm";
        public const string SQLQueryForm = "SQLQueryForm";
        public const string PermissionManagementForm = "PermissionManagementForm";
    }
    
    // Override Types
    public static class OverrideTypes
    {
        public const string UserManagement = "user_management";
        public const string FormSpecific = "form_specific";
    }
    
    // Cache Settings
    public static class CacheSettings
    {
        public const int DefaultCacheExpiryMinutes = 30;
        public const int MaxCachedUsers = 1000;
        public const int CacheRefreshIntervalMinutes = 15;
    }
}
```

### 5. Permission Validation Helper
**File**: `Modules/Helpers/Permissions/PermissionValidation.cs`

#### Validation Methods
```csharp
public static class PermissionValidation
{
    public static bool IsValidFormName(string formName);
    public static bool IsValidPermissionType(string permissionType);
    public static bool IsValidUserId(int userId);
    public static bool IsValidRoleId(int roleId);
    
    public static ValidationResult ValidatePermissionRequest(int userId, string formName, string permissionType);
    public static ValidationResult ValidatePermissionUpdate(int userId, string formName, string permissionType, bool isGranted);
    
    public class ValidationResult
    {
        public bool IsValid { get; set; }
        public string ErrorMessage { get; set; }
        public List<string> Warnings { get; set; }
    }
}
```

### 6. Performance Monitoring
**File**: `Modules/Helpers/Permissions/PermissionPerformanceMonitor.cs`

#### Performance Tracking
```csharp
public class PermissionPerformanceMonitor
{
    private readonly ConcurrentDictionary<string, PerformanceMetric> _metrics;
    
    public void RecordPermissionCheck(string operation, TimeSpan duration, bool cacheHit);
    public void RecordCacheOperation(string operation, TimeSpan duration, int itemCount);
    
    public PerformanceReport GenerateReport();
    public void ResetMetrics();
    
    public class PerformanceMetric
    {
        public string Operation { get; set; }
        public long TotalCalls { get; set; }
        public TimeSpan TotalDuration { get; set; }
        public TimeSpan AverageDuration { get; set; }
        public long CacheHits { get; set; }
        public long CacheMisses { get; set; }
        public double CacheHitRatio { get; set; }
    }
}
```

### 7. Service Initialization
**File**: `Modules/Services/PermissionServiceInitializer.cs`

#### Startup Integration
```csharp
public static class PermissionServiceInitializer
{
    public static void Initialize()
    {
        try
        {
            // Initialize permission service
            var service = PermissionService.Instance;
            
            // Setup UserManager integration
            PermissionServiceIntegration.InitializeWithUserManager();
            
            // Pre-load critical permissions
            PreloadCriticalPermissions();
            
            // Start performance monitoring
            StartPerformanceMonitoring();
            
            Debug.WriteLine("PermissionService initialized successfully");
        }
        catch (Exception ex)
        {
            Debug.WriteLine($"Failed to initialize PermissionService: {ex.Message}");
            throw;
        }
    }
    
    private static void PreloadCriticalPermissions()
    {
        // Pre-load permissions for current user if logged in
        if (UserManager.Instance.IsUserLoggedIn)
        {
            PermissionService.Instance.RefreshUserPermissions(
                UserManager.Instance.CurrentUser.UserId);
        }
    }
}
```

## Acceptance Criteria

### Service Implementation
- [ ] Singleton pattern implemented correctly
- [ ] Integration with existing UserManager service
- [ ] All core permission checking methods working
- [ ] Proper error handling and logging

### Caching System
- [ ] Fast permission lookups (target <1ms for cached)
- [ ] Intelligent cache invalidation
- [ ] Memory-efficient cache storage
- [ ] Cache statistics and monitoring

### Performance Requirements
- [ ] Permission checks under 1ms for cached permissions
- [ ] Cache load time under 100ms per user
- [ ] Memory usage under 1MB per cached user
- [ ] Cache hit ratio above 90% in normal usage

### Integration Quality
- [ ] Seamless integration with UserManager
- [ ] Compatible with existing service patterns
- [ ] Proper initialization and cleanup
- [ ] Thread-safe operations

### Code Quality
- [ ] All files under 500 lines
- [ ] Comprehensive error handling
- [ ] XML documentation for public methods
- [ ] Unit tests for core functionality

## Integration Points

### Existing System
- Uses `UserManager.Instance.CurrentUser` for current user context
- Follows `ParameterCacheService` pattern for caching
- Integrates with existing error handling and logging
- Compatible with current application lifecycle

### Future Tasks
- Provides foundation for Task 07 (MainFrame integration)
- Enables Task 08 (UserMasterForm integration)
- Supports Task 09 (PermissionManagementForm)
- Establishes service layer for all permission operations

## Testing Requirements

### Unit Testing
```csharp
[Test]
public void HasPermission_CachedUser_ReturnsFast()
{
    var stopwatch = Stopwatch.StartNew();
    var result = PermissionService.Instance.HasPermission(1, "EstimateForm", "Read");
    stopwatch.Stop();
    
    Assert.IsTrue(result);
    Assert.IsTrue(stopwatch.ElapsedMilliseconds < 1);
}

[Test]
public void RefreshUserPermissions_ValidUser_UpdatesCache()
{
    PermissionService.Instance.RefreshUserPermissions(1);
    var permissions = PermissionService.Instance.GetUserPermissions(1);
    
    Assert.IsNotNull(permissions);
    Assert.IsTrue(permissions.Count > 0);
}
```

### Performance Testing
- Test cache performance under load
- Validate memory usage with multiple users
- Test concurrent access scenarios
- Monitor cache hit ratios

### Integration Testing
- Test UserManager integration
- Validate service initialization
- Test error scenarios and recovery
- Verify thread safety

## Implementation Notes

### Caching Strategy
- Use ConcurrentDictionary for thread safety
- Implement time-based cache expiration
- Provide manual cache invalidation
- Monitor cache performance metrics

### Performance Optimization
- Minimize database calls through caching
- Use efficient data structures
- Implement lazy loading where appropriate
- Optimize for common permission check patterns

### Error Handling
- Graceful degradation when cache fails
- Fallback to database for critical operations
- Comprehensive logging for debugging
- Clear error messages for troubleshooting

## Files to Create/Modify

### New Files (7 files)
- `Modules/Services/PermissionService.cs`
- `Modules/Services/PermissionCacheService.cs`
- `Modules/Services/PermissionServiceIntegration.cs`
- `Modules/Services/PermissionServiceInitializer.cs`
- `Modules/Helpers/Permissions/PermissionConstants.cs`
- `Modules/Helpers/Permissions/PermissionValidation.cs`
- `Modules/Helpers/Permissions/PermissionPerformanceMonitor.cs`

## Dependencies
- Task 04: Permission Models and Repository (REQUIRED)
- Existing UserManager service
- Existing ParameterCacheService (for pattern reference)

## Estimated Effort
- 8-10 hours for core service implementation
- 6-8 hours for caching system
- 4-6 hours for integration and validation
- 4-6 hours for testing and performance optimization
