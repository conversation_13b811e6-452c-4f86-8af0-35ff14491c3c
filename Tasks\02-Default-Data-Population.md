# Task 02: Default Data Population and Configuration

## Objective
Populate the RBAC database schema with default roles, forms, permission types, and establish baseline permissions for the ProManage system.

## Prerequisites
- Task 01 (RBAC Database Schema Setup) completed successfully
- All 5 RBAC tables created and validated
- Users table extended with role_id column
- Database connection established

## Scope
Populate the RBAC system with production-ready default data:
1. Create standard roles (Administrator, User, Manager)
2. Register all permission-controlled forms
3. Define permission types (Read, New, Edit, Delete)
4. Establish default role-based permissions
5. Configure form categories and metadata

## Deliverables

### 1. Default Data Population Script
**File**: `Modules/Procedures/Permissions/02-Default-Data-Population.sql`

**Content Requirements**:
- Transaction-wrapped data insertion
- Idempotent operations (safe to run multiple times)
- Comprehensive error handling
- Data validation and verification
- Clear logging of population steps

### 2. Default Roles Configuration

#### Standard Roles
```sql
-- Administrator Role
INSERT INTO roles (role_name, description, is_active) VALUES
('Administrator', 'Full system access with all permissions', true);

-- Manager Role  
INSERT INTO roles (role_name, description, is_active) VALUES
('Manager', 'Business operations with limited system access', true);

-- User Role
INSERT INTO roles (role_name, description, is_active) VALUES
('User', 'Basic user with restricted permissions', true);

-- Read-Only Role
INSERT INTO roles (role_name, description, is_active) VALUES
('ReadOnly', 'View-only access to business data', true);
```

### 3. Forms Registration

#### Permission-Controlled Forms
Based on analysis in `docs/Users and Role management.md` section 4.4:

```sql
-- Business Forms
INSERT INTO forms (form_name, display_name, category, requires_permissions) VALUES
('EstimateForm', 'Estimate Management', 'business', true);

-- System Management Forms
INSERT INTO forms (form_name, display_name, category, requires_permissions) VALUES
('DatabaseForm', 'Database Configuration', 'system', true),
('ParametersForm', 'System Parameters', 'system', true),
('RoleMasterForm', 'Role Management', 'system', true),
('SQLQueryForm', 'SQL Query Tool', 'system', true);

-- User Management Forms
INSERT INTO forms (form_name, display_name, category, requires_permissions) VALUES
('UserManagementListForm', 'User List Management', 'user_management', true),
('UserMasterForm', 'User Entry Form', 'user_management', true),
('PermissionManagementForm', 'Permission Management', 'user_management', true);
```

### 4. Permission Types Definition

#### Standard Permission Types
```sql
INSERT INTO permission_types (permission_name, description, is_active) VALUES
('Read', 'View and access form data', true),
('New', 'Create new records', true),
('Edit', 'Modify existing records', true),
('Delete', 'Remove records', true);
```

### 5. Default Role Permissions Matrix

#### Administrator Role (Full Access)
```sql
-- Administrator gets all permissions for all forms
INSERT INTO role_perms (role_id, form_name, permission_name, is_granted)
SELECT r.role_id, f.form_name, pt.permission_name, true
FROM roles r
CROSS JOIN forms f
CROSS JOIN permission_types pt
WHERE r.role_name = 'Administrator'
AND f.requires_permissions = true
AND pt.is_active = true;
```

#### Manager Role (Business + Limited System)
```sql
-- Business forms: Full access
INSERT INTO role_perms (role_id, form_name, permission_name, is_granted)
SELECT r.role_id, f.form_name, pt.permission_name, true
FROM roles r
CROSS JOIN forms f
CROSS JOIN permission_types pt
WHERE r.role_name = 'Manager'
AND f.category = 'business'
AND pt.is_active = true;

-- User management: Read only
INSERT INTO role_perms (role_id, form_name, permission_name, is_granted)
SELECT r.role_id, f.form_name, 'Read', true
FROM roles r
CROSS JOIN forms f
WHERE r.role_name = 'Manager'
AND f.category = 'user_management';

-- System forms: No access (explicitly denied)
INSERT INTO role_perms (role_id, form_name, permission_name, is_granted)
SELECT r.role_id, f.form_name, pt.permission_name, false
FROM roles r
CROSS JOIN forms f
CROSS JOIN permission_types pt
WHERE r.role_name = 'Manager'
AND f.category = 'system'
AND pt.is_active = true;
```

#### User Role (Business Read/Edit Only)
```sql
-- Business forms: Read and Edit only
INSERT INTO role_perms (role_id, form_name, permission_name, is_granted)
VALUES 
((SELECT role_id FROM roles WHERE role_name = 'User'), 'EstimateForm', 'Read', true),
((SELECT role_id FROM roles WHERE role_name = 'User'), 'EstimateForm', 'Edit', true),
((SELECT role_id FROM roles WHERE role_name = 'User'), 'EstimateForm', 'New', false),
((SELECT role_id FROM roles WHERE role_name = 'User'), 'EstimateForm', 'Delete', false);

-- All other forms: No access
INSERT INTO role_perms (role_id, form_name, permission_name, is_granted)
SELECT r.role_id, f.form_name, pt.permission_name, false
FROM roles r
CROSS JOIN forms f
CROSS JOIN permission_types pt
WHERE r.role_name = 'User'
AND f.form_name != 'EstimateForm'
AND pt.is_active = true;
```

#### ReadOnly Role (View Only)
```sql
-- All forms: Read only
INSERT INTO role_perms (role_id, form_name, permission_name, is_granted)
SELECT r.role_id, f.form_name, 
       CASE WHEN pt.permission_name = 'Read' THEN true ELSE false END,
       pt.permission_name
FROM roles r
CROSS JOIN forms f
CROSS JOIN permission_types pt
WHERE r.role_name = 'ReadOnly'
AND f.requires_permissions = true
AND pt.is_active = true;
```

### 6. Data Validation Queries

#### Verification Script
```sql
-- Verify roles created
SELECT role_id, role_name, description, is_active FROM roles ORDER BY role_name;

-- Verify forms registered
SELECT form_id, form_name, display_name, category FROM forms ORDER BY category, form_name;

-- Verify permission types
SELECT permission_id, permission_name, description FROM permission_types ORDER BY permission_name;

-- Verify role permissions count
SELECT r.role_name, COUNT(*) as permission_count
FROM roles r
JOIN role_perms rp ON r.role_id = rp.role_id
GROUP BY r.role_name
ORDER BY r.role_name;

-- Verify permission distribution
SELECT f.form_name, pt.permission_name, 
       COUNT(CASE WHEN rp.is_granted THEN 1 END) as granted_count,
       COUNT(CASE WHEN NOT rp.is_granted THEN 1 END) as denied_count
FROM forms f
CROSS JOIN permission_types pt
LEFT JOIN role_perms rp ON f.form_name = rp.form_name AND pt.permission_name = rp.permission_name
GROUP BY f.form_name, pt.permission_name
ORDER BY f.form_name, pt.permission_name;
```

## Acceptance Criteria

### Data Population
- [ ] 4 default roles created (Administrator, Manager, User, ReadOnly)
- [ ] 8 forms registered with correct categories
- [ ] 4 permission types defined (Read, New, Edit, Delete)
- [ ] Complete role permissions matrix populated
- [ ] All data validation queries return expected results

### Role Permission Verification
- [ ] Administrator has all permissions for all forms
- [ ] Manager has business access + user management read
- [ ] User has limited EstimateForm access only
- [ ] ReadOnly has read-only access to all forms
- [ ] No orphaned permissions or missing combinations

### Data Integrity
- [ ] All foreign key relationships valid
- [ ] No duplicate role-form-permission combinations
- [ ] All required forms have permission entries
- [ ] Permission counts match expected totals

### Script Quality
- [ ] Idempotent execution (safe to run multiple times)
- [ ] Transaction safety with rollback capability
- [ ] Clear error messages and logging
- [ ] Comprehensive verification queries included

## Integration Points

### Existing System
- Uses roles created in Task 01 schema
- Prepares data for Task 03 user migration
- Establishes foundation for permission service implementation
- Compatible with existing form structure

### Future Tasks
- Enables Task 03 user migration to map existing users
- Provides data for Task 04 permission models and repository
- Supports Task 05 permission service implementation
- Establishes baseline for permission management UI

## Testing Requirements

### Data Validation
```sql
-- Test role permissions completeness
SELECT COUNT(*) as total_permissions FROM role_perms;
-- Expected: 128 permissions (4 roles × 8 forms × 4 permissions)

-- Test Administrator permissions
SELECT COUNT(*) as admin_permissions FROM role_perms rp
JOIN roles r ON rp.role_id = r.role_id
WHERE r.role_name = 'Administrator' AND rp.is_granted = true;
-- Expected: 32 permissions (8 forms × 4 permissions)

-- Test form registration
SELECT COUNT(*) as registered_forms FROM forms WHERE requires_permissions = true;
-- Expected: 8 forms
```

### Permission Logic Testing
- Verify each role has appropriate access levels
- Test permission inheritance patterns
- Validate form categorization
- Confirm no security gaps in default permissions

## Implementation Notes

### Security Considerations
- Administrator role should have complete access
- User role should have minimal necessary permissions
- ReadOnly role provides safe audit access
- No forms should be accessible without explicit permissions

### Scalability Design
- Form registration supports dynamic addition
- Permission types can be extended
- Role structure supports organizational hierarchy
- Default permissions provide secure baseline

### Data Consistency
- Use transactions for atomic operations
- Validate all foreign key relationships
- Ensure complete permission coverage
- Provide verification for all data

## Files to Create/Modify
- `Modules/Procedures/Permissions/02-Default-Data-Population.sql` (NEW)

## Dependencies
- Task 01: RBAC Database Schema Setup (REQUIRED)

## Estimated Effort
- 3-4 hours for data design and population script
- Additional 2 hours for testing and validation
