# ProManage RBAC System Migration Guide

## Overview

This guide helps you migrate from the **5-table approach** (with unnecessary complexity) to the **3-table approach** (simplified and optimized) for the ProManage RBAC system.

## Current Situation Analysis

You currently have **5 tables** created from the original script:
1. ✅ `roles` (required - keep)
2. ✅ `role_perms` (required - keep, add validation)
3. ✅ `user_perms` (required - keep, add validation)
4. ❌ `forms` (not required - remove)
5. ❌ `permission_types` (not required - remove)

## Migration Steps

### Step 1: Backup Your Database
```sql
-- Create a backup before making changes
pg_dump your_database_name > rbac_backup_$(date +%Y%m%d_%H%M%S).sql
```

### Step 2: Remove Unnecessary Tables
Run the cleanup script to remove the `forms` and `permission_types` tables:

```sql
\i docs/RBAC-Cleanup-Unnecessary-Tables.sql
```

**What this does:**
- Drops `forms` table (form names will be hardcoded in application)
- Drops `permission_types` table (permission types will be hardcoded in application)
- Verifies the 3 required tables remain intact
- Shows summary of what was removed

### Step 3: Add Validation Constraints
Run the validation script to add proper constraints to the remaining tables:

```sql
\i docs/RBAC-Add-Validation-Constraints.sql
```

**What this does:**
- Adds CHECK constraints to `role_perms.form_name` (validates against 7 hardcoded forms)
- Adds CHECK constraints to `role_perms.permission_name` (validates against 4 hardcoded permissions)
- Adds CHECK constraints to `user_perms.form_name` (same validation as role_perms)
- Adds CHECK constraints to `user_perms.permission_name` (same validation as role_perms)
- Validates existing data doesn't violate the new constraints

### Step 4: Verify Migration Success
Check that everything is working correctly:

```sql
-- Verify only 3 tables exist
SELECT table_name 
FROM information_schema.tables 
WHERE table_name IN ('roles', 'role_perms', 'user_perms', 'forms', 'permission_types')
ORDER BY table_name;

-- Should show only: roles, role_perms, user_perms

-- Verify constraints are in place
SELECT constraint_name, table_name, constraint_type
FROM information_schema.table_constraints
WHERE table_name IN ('role_perms', 'user_perms')
AND constraint_type = 'CHECK'
ORDER BY table_name, constraint_name;

-- Should show validation constraints for form_name and permission_name
```

## What Changed

### Before (5-Table Approach)
```
roles ──┐
        ├── role_perms (references forms.form_name, permission_types.permission_name)
        │
forms ──┘
permission_types ──┘
user_perms (references forms.form_name, permission_types.permission_name)
```

### After (3-Table Approach)
```
roles ──┐
        ├── role_perms (hardcoded form_name, permission_name with CHECK constraints)
        │
user_perms (hardcoded form_name, permission_name with CHECK constraints)
```

## Hardcoded Values

### Form Names (7 forms)
```
'EstimateForm'
'DatabaseForm' 
'ParametersForm'
'RoleMasterForm'
'SQLQueryForm'
'UserManagementListForm'
'UserMasterForm'
```

### Permission Types (4 permissions)
```
'Read'
'New'
'Edit'
'Delete'
```

## Benefits of 3-Table Approach

1. **Reduced Complexity**: 40% fewer tables to manage
2. **Better Performance**: Fewer JOINs in permission queries
3. **Simpler Maintenance**: No need to manage form/permission registries
4. **Stable Schema**: Form names and permissions rarely change
5. **Easier Deployment**: Less database setup required

## Application Code Changes Required

### Constants File
Create application constants for the hardcoded values:

```csharp
public static class PermissionConstants
{
    public static readonly string[] ValidForms = {
        "EstimateForm", "DatabaseForm", "ParametersForm", 
        "RoleMasterForm", "SQLQueryForm", "UserManagementListForm", "UserMasterForm"
    };
    
    public static readonly string[] ValidPermissions = {
        "Read", "New", "Edit", "Delete"
    };
}
```

### Remove Dynamic Form Registration
- Remove any code that queries `forms` or `permission_types` tables
- Replace with references to the constants above
- Update permission queries to use hardcoded values

## Rollback Plan

If you need to rollback to the 5-table approach:

1. Restore from backup:
   ```sql
   psql your_database_name < rbac_backup_YYYYMMDD_HHMMSS.sql
   ```

2. Or recreate the tables manually:
   ```sql
   \i docs/RBAC-Complete-Setup.sql  -- Original 5-table version
   ```

## Testing Checklist

After migration, verify:

- [ ] All 3 required tables exist (`roles`, `role_perms`, `user_perms`)
- [ ] Unnecessary tables are removed (`forms`, `permission_types`)
- [ ] Validation constraints are in place
- [ ] Existing permission data is intact
- [ ] Permission queries still work correctly
- [ ] Application can connect and authenticate users
- [ ] Form access control works as expected

## Support

If you encounter issues during migration:

1. **Check the logs** for any constraint violations
2. **Verify data integrity** before and after each step
3. **Test with a small dataset** first if possible
4. **Keep backups** at each major step

The migration is designed to be safe and reversible, but always test in a development environment first.
