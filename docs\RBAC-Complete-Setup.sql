-- =====================================================
-- ProManage Role-Based Access Control (RBAC) System
-- Complete Database Setup Script (3-Table Approach)
-- =====================================================
--
-- This script creates the complete RBAC system for ProManage
-- using the simplified 3-table approach with hardcoded form names
-- and permission types for reduced database complexity.
--
-- Tables Created:
-- 1. roles - Role definitions
-- 2. role_perms - Role-based permissions
-- 3. user_perms - User overrides and global restrictions
--
-- Form names and permission types are hardcoded in application
-- constants instead of stored in database tables.
--
-- Prerequisites:
-- - PostgreSQL database connection established
-- - Sufficient privileges to create tables and modify schema
-- - Database backup recommended before execution
--
-- If you already have the 5-table version, run these scripts first:
-- 1. RBAC-Cleanup-Unnecessary-Tables.sql (remove forms/permission_types tables)
-- 2. RBAC-Add-Validation-Constraints.sql (add validation to existing tables)
--
-- =====================================================

-- Start transaction for atomic operation
BEGIN;

-- Set error handling
\set ON_ERROR_STOP on

-- =====================================================
-- SECTION 1: VALIDATION AND PREREQUISITES
-- =====================================================

DO $$
BEGIN
    -- Check if users table exists
    IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'users') THEN
        RAISE EXCEPTION 'Users table does not exist. Please ensure the base ProManage database is set up first.';
    END IF;
    
    -- Check if RBAC tables already exist
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'roles') THEN
        RAISE NOTICE 'RBAC tables already exist. This script will skip table creation and only update data.';
    END IF;
    
    RAISE NOTICE 'Prerequisites validated successfully.';
END $$;

-- =====================================================
-- SECTION 2: TABLE CREATION
-- =====================================================

-- Create roles table
CREATE TABLE IF NOT EXISTS roles (
    role_id SERIAL PRIMARY KEY,
    role_name VARCHAR(50) UNIQUE NOT NULL,
    description TEXT,
    is_active BOOLEAN DEFAULT TRUE NOT NULL,
    created_date TIMESTAMP DEFAULT NOW() NOT NULL,
    updated_date TIMESTAMP DEFAULT NOW() NOT NULL
);

-- NOTE: forms and permission_types tables are NOT REQUIRED
-- Form names and permission types are hardcoded in application constants
-- This reduces database complexity while maintaining functionality

-- Create role_perms table (optimized name)
CREATE TABLE IF NOT EXISTS role_perms (
    perm_id SERIAL PRIMARY KEY,
    role_id INTEGER NOT NULL REFERENCES roles(role_id) ON DELETE CASCADE,
    form_name VARCHAR(100) NOT NULL,
    permission_name VARCHAR(20) NOT NULL,
    is_granted BOOLEAN NOT NULL,
    created_date TIMESTAMP DEFAULT NOW() NOT NULL,
    updated_date TIMESTAMP DEFAULT NOW() NOT NULL,
    UNIQUE(role_id, form_name, permission_name)
);

-- Create user_perms table (optimized name)
CREATE TABLE IF NOT EXISTS user_perms (
    perm_id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL REFERENCES users(user_id) ON DELETE CASCADE,
    override_type VARCHAR(20) NOT NULL,
    form_name VARCHAR(100) NOT NULL,
    permission_name VARCHAR(20) NOT NULL,
    is_granted BOOLEAN NOT NULL,
    created_date TIMESTAMP DEFAULT NOW() NOT NULL,
    updated_date TIMESTAMP DEFAULT NOW() NOT NULL,
    UNIQUE(user_id, override_type, form_name, permission_name)
);

-- Add role_id to users table if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'users' AND column_name = 'role_id') THEN
        ALTER TABLE users ADD COLUMN role_id INTEGER;
        RAISE NOTICE 'Added role_id column to users table.';
    ELSE
        RAISE NOTICE 'role_id column already exists in users table.';
    END IF;
END $$;

-- =====================================================
-- SECTION 3: INDEX CREATION
-- =====================================================

-- Indexes for roles table
CREATE INDEX IF NOT EXISTS idx_roles_name ON roles(role_name);
CREATE INDEX IF NOT EXISTS idx_roles_active ON roles(is_active);

-- NOTE: Indexes for forms and permission_types tables removed
-- These tables are not required in the simplified 3-table approach

-- Indexes for role_perms table
CREATE INDEX IF NOT EXISTS idx_role_perms_lookup ON role_perms(role_id, form_name, permission_name);
CREATE INDEX IF NOT EXISTS idx_role_perms_role ON role_perms(role_id);
CREATE INDEX IF NOT EXISTS idx_role_perms_form ON role_perms(form_name);

-- Indexes for user_perms table
CREATE INDEX IF NOT EXISTS idx_user_perms_lookup ON user_perms(user_id, override_type, form_name, permission_name);
CREATE INDEX IF NOT EXISTS idx_user_perms_user ON user_perms(user_id);
CREATE INDEX IF NOT EXISTS idx_user_perms_type ON user_perms(override_type);

-- Index for users table
CREATE INDEX IF NOT EXISTS idx_users_role_id ON users(role_id);

-- =====================================================
-- SECTION 4: CONSTRAINT CREATION
-- =====================================================

-- Add constraint for user_perms override types
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.table_constraints
                   WHERE constraint_name = 'chk_user_perms_type') THEN
        ALTER TABLE user_perms ADD CONSTRAINT chk_user_perms_type
            CHECK (override_type IN ('user_management', 'form_specific'));
        RAISE NOTICE 'Added constraint chk_user_perms_type.';
    ELSE
        RAISE NOTICE 'Constraint chk_user_perms_type already exists.';
    END IF;
END $$;

-- Add constraint for valid form names (hardcoded list)
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.table_constraints
                   WHERE constraint_name = 'chk_role_perms_form_name') THEN
        ALTER TABLE role_perms ADD CONSTRAINT chk_role_perms_form_name
            CHECK (form_name IN ('EstimateForm', 'DatabaseForm', 'ParametersForm', 'RoleMasterForm', 'SQLQueryForm', 'UserManagementListForm', 'UserMasterForm'));
        RAISE NOTICE 'Added constraint chk_role_perms_form_name.';
    ELSE
        RAISE NOTICE 'Constraint chk_role_perms_form_name already exists.';
    END IF;
END $$;

-- Add constraint for valid permission names (hardcoded list)
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.table_constraints
                   WHERE constraint_name = 'chk_role_perms_permission_name') THEN
        ALTER TABLE role_perms ADD CONSTRAINT chk_role_perms_permission_name
            CHECK (permission_name IN ('Read', 'New', 'Edit', 'Delete'));
        RAISE NOTICE 'Added constraint chk_role_perms_permission_name.';
    ELSE
        RAISE NOTICE 'Constraint chk_role_perms_permission_name already exists.';
    END IF;
END $$;

-- Add constraint for valid form names in user_perms (same as role_perms)
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.table_constraints
                   WHERE constraint_name = 'chk_user_perms_form_name') THEN
        ALTER TABLE user_perms ADD CONSTRAINT chk_user_perms_form_name
            CHECK (form_name IN ('EstimateForm', 'DatabaseForm', 'ParametersForm', 'RoleMasterForm', 'SQLQueryForm', 'UserManagementListForm', 'UserMasterForm'));
        RAISE NOTICE 'Added constraint chk_user_perms_form_name.';
    ELSE
        RAISE NOTICE 'Constraint chk_user_perms_form_name already exists.';
    END IF;
END $$;

-- Add constraint for valid permission names in user_perms (same as role_perms)
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.table_constraints
                   WHERE constraint_name = 'chk_user_perms_permission_name') THEN
        ALTER TABLE user_perms ADD CONSTRAINT chk_user_perms_permission_name
            CHECK (permission_name IN ('Read', 'New', 'Edit', 'Delete'));
        RAISE NOTICE 'Added constraint chk_user_perms_permission_name.';
    ELSE
        RAISE NOTICE 'Constraint chk_user_perms_permission_name already exists.';
    END IF;
END $$;

-- =====================================================
-- SECTION 5: DEFAULT DATA POPULATION
-- =====================================================

-- Insert default roles (with conflict handling)
INSERT INTO roles (role_name, description) VALUES
('Administrator', 'Full system access with all permissions'),
('Manager', 'Management level access with most permissions'),
('User', 'Standard user with limited permissions'),
('ReadOnly', 'Read-only access to most forms')
ON CONFLICT (role_name) DO NOTHING;

-- NOTE: Data population for forms and permission_types tables removed
-- Form names and permission types are hardcoded in application constants:
-- Forms: EstimateForm, DatabaseForm, ParametersForm, RoleMasterForm, SQLQueryForm, UserManagementListForm, UserMasterForm
-- Permissions: Read, New, Edit, Delete

-- =====================================================
-- SECTION 6: DATA MIGRATION
-- =====================================================

-- Migrate existing user roles to role_id references
DO $$
DECLARE
    admin_role_id INTEGER;
    manager_role_id INTEGER;
    user_role_id INTEGER;
    migration_count INTEGER;
BEGIN
    -- Get role IDs
    SELECT role_id INTO admin_role_id FROM roles WHERE role_name = 'Administrator';
    SELECT role_id INTO manager_role_id FROM roles WHERE role_name = 'Manager';
    SELECT role_id INTO user_role_id FROM roles WHERE role_name = 'User';

    -- Update users with role_id based on existing role column
    UPDATE users SET role_id = (
        CASE
            WHEN LOWER(role) = 'admin' THEN admin_role_id
            WHEN LOWER(role) = 'manager' THEN manager_role_id
            WHEN LOWER(role) = 'user' THEN user_role_id
            ELSE user_role_id  -- Default to User role
        END
    )
    WHERE role_id IS NULL;

    GET DIAGNOSTICS migration_count = ROW_COUNT;
    RAISE NOTICE 'Migrated % users to new role system.', migration_count;
END $$;

-- Add foreign key constraint for users table (after data migration)
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.table_constraints
                   WHERE constraint_name = 'fk_users_role_id') THEN
        ALTER TABLE users ADD CONSTRAINT fk_users_role_id
            FOREIGN KEY (role_id) REFERENCES roles(role_id);
        RAISE NOTICE 'Added foreign key constraint fk_users_role_id.';
    ELSE
        RAISE NOTICE 'Foreign key constraint fk_users_role_id already exists.';
    END IF;
END $$;

-- Make role_id required after population
DO $$
BEGIN
    -- Check if any users still have NULL role_id
    IF EXISTS (SELECT 1 FROM users WHERE role_id IS NULL) THEN
        RAISE WARNING 'Some users still have NULL role_id. Setting them to User role.';
        UPDATE users SET role_id = (SELECT role_id FROM roles WHERE role_name = 'User')
        WHERE role_id IS NULL;
    END IF;

    -- Make role_id NOT NULL
    ALTER TABLE users ALTER COLUMN role_id SET NOT NULL;
    RAISE NOTICE 'Made role_id column NOT NULL.';
EXCEPTION
    WHEN OTHERS THEN
        RAISE NOTICE 'role_id column may already be NOT NULL or constraint already exists.';
END $$;

-- =====================================================
-- SECTION 7: DEFAULT PERMISSIONS SETUP
-- =====================================================

-- Create default Administrator permissions (full access to all forms)
DO $$
DECLARE
    admin_role_id INTEGER;
    form_names TEXT[] := ARRAY['EstimateForm', 'DatabaseForm', 'ParametersForm', 'RoleMasterForm', 'SQLQueryForm', 'UserManagementListForm', 'UserMasterForm'];
    permission_names TEXT[] := ARRAY['Read', 'New', 'Edit', 'Delete'];
    form_name TEXT;
    permission_name TEXT;
BEGIN
    SELECT role_id INTO admin_role_id FROM roles WHERE role_name = 'Administrator';

    -- Grant all permissions to Administrator for all forms
    FOREACH form_name IN ARRAY form_names LOOP
        FOREACH permission_name IN ARRAY permission_names LOOP
            INSERT INTO role_perms (role_id, form_name, permission_name, is_granted)
            VALUES (admin_role_id, form_name, permission_name, true)
            ON CONFLICT (role_id, form_name, permission_name) DO NOTHING;
        END LOOP;
    END LOOP;

    RAISE NOTICE 'Created default Administrator permissions.';
END $$;

-- Create default User permissions (limited access)
DO $$
DECLARE
    user_role_id INTEGER;
BEGIN
    SELECT role_id INTO user_role_id FROM roles WHERE role_name = 'User';

    -- Grant basic permissions to User role
    INSERT INTO role_perms (role_id, form_name, permission_name, is_granted) VALUES
    (user_role_id, 'EstimateForm', 'Read', true),
    (user_role_id, 'EstimateForm', 'New', true),
    (user_role_id, 'EstimateForm', 'Edit', true),
    (user_role_id, 'UserMasterForm', 'Read', true)
    ON CONFLICT (role_id, form_name, permission_name) DO NOTHING;

    RAISE NOTICE 'Created default User permissions.';
END $$;

-- =====================================================
-- SECTION 8: VERIFICATION AND VALIDATION
-- =====================================================

-- Verify table creation (3 tables only)
DO $$
DECLARE
    table_count INTEGER;
    expected_tables TEXT[] := ARRAY['roles', 'role_perms', 'user_perms'];
    table_name TEXT;
BEGIN
    RAISE NOTICE 'Verifying table creation (3-table approach)...';

    FOREACH table_name IN ARRAY expected_tables LOOP
        SELECT COUNT(*) INTO table_count
        FROM information_schema.tables
        WHERE table_name = table_name;

        IF table_count = 0 THEN
            RAISE EXCEPTION 'Table % was not created successfully.', table_name;
        ELSE
            RAISE NOTICE 'Table % created successfully.', table_name;
        END IF;
    END LOOP;
END $$;

-- Verify data population (3-table approach)
DO $$
DECLARE
    role_count INTEGER;
    user_migration_count INTEGER;
BEGIN
    RAISE NOTICE 'Verifying data population (3-table approach)...';

    SELECT COUNT(*) INTO role_count FROM roles;
    SELECT COUNT(*) INTO user_migration_count FROM users WHERE role_id IS NOT NULL;

    RAISE NOTICE 'Roles created: %', role_count;
    RAISE NOTICE 'Users migrated: %', user_migration_count;
    RAISE NOTICE 'Form names: Hardcoded in application (7 forms)';
    RAISE NOTICE 'Permission types: Hardcoded in application (4 types)';

    IF role_count < 4 THEN
        RAISE WARNING 'Expected at least 4 roles, found %.', role_count;
    END IF;
END $$;

-- Verify permissions setup
DO $$
DECLARE
    admin_perm_count INTEGER;
    user_perm_count INTEGER;
BEGIN
    RAISE NOTICE 'Verifying permissions setup...';

    SELECT COUNT(*) INTO admin_perm_count
    FROM role_perms rp
    JOIN roles r ON rp.role_id = r.role_id
    WHERE r.role_name = 'Administrator';

    SELECT COUNT(*) INTO user_perm_count
    FROM role_perms rp
    JOIN roles r ON rp.role_id = r.role_id
    WHERE r.role_name = 'User';

    RAISE NOTICE 'Administrator permissions: %', admin_perm_count;
    RAISE NOTICE 'User permissions: %', user_perm_count;
END $$;

-- Final verification query (3-table approach)
SELECT
    'RBAC Setup Summary (3-Table Approach)' as component,
    (SELECT COUNT(*) FROM roles) as roles_count,
    '7 (hardcoded)' as forms_count,
    '4 (hardcoded)' as permission_types_count,
    (SELECT COUNT(*) FROM role_perms) as role_permissions_count,
    (SELECT COUNT(*) FROM users WHERE role_id IS NOT NULL) as migrated_users_count;

-- =====================================================
-- SECTION 9: COMPLETION AND COMMIT
-- =====================================================

-- Success message
DO $$
BEGIN
    RAISE NOTICE '=====================================================';
    RAISE NOTICE 'ProManage RBAC System Setup Completed Successfully!';
    RAISE NOTICE '=====================================================';
    RAISE NOTICE 'Next Steps:';
    RAISE NOTICE '1. Deploy PermissionService and PermissionRepository classes';
    RAISE NOTICE '2. Update forms with permission checks';
    RAISE NOTICE '3. Implement PermissionManagementForm UI';
    RAISE NOTICE '4. Test permission system functionality';
    RAISE NOTICE '=====================================================';
END $$;

-- Commit the transaction
COMMIT;

-- Final success message
\echo 'RBAC database setup completed successfully!'
\echo 'All tables created, data populated, and users migrated.'
\echo 'The permission system is ready for application integration.'
