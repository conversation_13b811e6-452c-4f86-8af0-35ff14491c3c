# Task 08: UserMasterForm Permissions Tab Replacement

## Objective
Replace the existing basic permissions tab in UserMasterForm with a comprehensive permission display grid that shows effective permissions, inheritance, and provides access to permission management.

## Prerequisites
- Task 05 (Permission Service and Caching) completed
- Task 06 (Permission Helpers and Utilities) completed
- Understanding of existing UserMasterForm structure and tab layout
- Access to current UserMasterForm implementation

## Scope
Transform UserMasterForm permissions functionality:
1. Replace existing permission checkboxes with comprehensive grid
2. Display effective permissions with source attribution
3. Add permission management button for authorized users
4. Integrate with existing form lifecycle and validation
5. Maintain compatibility with existing UserMasterForm patterns

## Deliverables

### 1. UserMasterForm Designer Updates
**File**: `UserMasterForm.Designer.cs` (MODIFY EXISTING)

#### Remove Existing Permission Controls
```csharp
// Remove these existing controls from xtraTabPage2 (Permissions tab):
// - groupActionPermissions
// - chkCreateAction
// - chkReadAction  
// - chkUpdateAction
// - chkDeleteAction
// - chkExportAction
// - chkPrintAction

// Replace with new permission grid controls
private DevExpress.XtraGrid.GridControl gridUserPermissions;
private DevExpress.XtraGrid.Views.Grid.GridView viewUserPermissions;
private DevExpress.XtraEditors.SimpleButton btnManagePermissions;
private DevExpress.XtraEditors.LabelControl lblPermissionNote;
private DevExpress.XtraEditors.PanelControl panelPermissionControls;
private DevExpress.XtraEditors.LabelControl lblPermissionSummary;
```

#### New Control Layout
```csharp
// Configure permission grid
this.gridUserPermissions.Dock = System.Windows.Forms.DockStyle.Fill;
this.gridUserPermissions.Location = new System.Drawing.Point(0, 0);
this.gridUserPermissions.MainView = this.viewUserPermissions;
this.gridUserPermissions.Name = "gridUserPermissions";
this.gridUserPermissions.Size = new System.Drawing.Size(800, 400);
this.gridUserPermissions.TabIndex = 0;
this.gridUserPermissions.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
    this.viewUserPermissions});

// Configure grid view
this.viewUserPermissions.GridControl = this.gridUserPermissions;
this.viewUserPermissions.Name = "viewUserPermissions";
this.viewUserPermissions.OptionsView.ShowGroupPanel = false;
this.viewUserPermissions.OptionsSelection.EnableAppearanceFocusedCell = false;
this.viewUserPermissions.OptionsSelection.EnableAppearanceFocusedRow = true;
this.viewUserPermissions.OptionsView.ShowIndicator = false;
this.viewUserPermissions.OptionsView.RowAutoHeight = true;

// Configure control panel
this.panelPermissionControls.Dock = System.Windows.Forms.DockStyle.Bottom;
this.panelPermissionControls.Height = 80;
this.panelPermissionControls.Location = new System.Drawing.Point(0, 400);
this.panelPermissionControls.Name = "panelPermissionControls";

// Configure manage permissions button
this.btnManagePermissions.Location = new System.Drawing.Point(10, 10);
this.btnManagePermissions.Name = "btnManagePermissions";
this.btnManagePermissions.Size = new System.Drawing.Size(150, 30);
this.btnManagePermissions.TabIndex = 0;
this.btnManagePermissions.Text = "Manage Permissions...";
this.btnManagePermissions.Click += new System.EventHandler(this.btnManagePermissions_Click);

// Configure permission note label
this.lblPermissionNote.Location = new System.Drawing.Point(170, 15);
this.lblPermissionNote.Name = "lblPermissionNote";
this.lblPermissionNote.Size = new System.Drawing.Size(400, 20);
this.lblPermissionNote.TabIndex = 1;
this.lblPermissionNote.Text = "This view shows effective permissions for this user. Click 'Manage Permissions' to modify.";

// Configure permission summary label
this.lblPermissionSummary.Location = new System.Drawing.Point(10, 50);
this.lblPermissionSummary.Name = "lblPermissionSummary";
this.lblPermissionSummary.Size = new System.Drawing.Size(600, 20);
this.lblPermissionSummary.TabIndex = 2;
this.lblPermissionSummary.Text = "";

// Add controls to tab page
this.xtraTabPage2.Controls.Clear(); // Remove existing controls
this.xtraTabPage2.Controls.Add(this.gridUserPermissions);
this.xtraTabPage2.Controls.Add(this.panelPermissionControls);
this.panelPermissionControls.Controls.Add(this.btnManagePermissions);
this.panelPermissionControls.Controls.Add(this.lblPermissionNote);
this.panelPermissionControls.Controls.Add(this.lblPermissionSummary);
```

### 2. UserMasterForm Code Updates
**File**: `UserMasterForm.cs` (MODIFY EXISTING)

#### Permission Grid Data Source
```csharp
// Add to existing UserMasterForm class
private List<PermissionDisplayModel> _userPermissions;

public class PermissionDisplayModel
{
    public string FormName { get; set; }
    public string DisplayName { get; set; }
    public string Category { get; set; }
    public bool CanRead { get; set; }
    public bool CanNew { get; set; }
    public bool CanEdit { get; set; }
    public bool CanDelete { get; set; }
    public string ReadSource { get; set; }
    public string NewSource { get; set; }
    public string EditSource { get; set; }
    public string DeleteSource { get; set; }
    public bool HasOverrides { get; set; }
    public bool HasRestrictions { get; set; }
}
```

#### Permission Loading Methods
```csharp
private void LoadUserPermissions()
{
    try
    {
        if (_currentUser?.UserId > 0)
        {
            _userPermissions = GetUserPermissionDisplay(_currentUser.UserId);
            gridUserPermissions.DataSource = _userPermissions;
            ConfigurePermissionGrid();
            UpdatePermissionSummary();
        }
        else
        {
            _userPermissions = new List<PermissionDisplayModel>();
            gridUserPermissions.DataSource = null;
            lblPermissionSummary.Text = "No user selected";
        }
    }
    catch (Exception ex)
    {
        Debug.WriteLine($"Error loading user permissions: {ex.Message}");
        MessageBox.Show("Error loading permissions. Please try again.", "Error", 
            MessageBoxButtons.OK, MessageBoxIcon.Warning);
    }
}

private List<PermissionDisplayModel> GetUserPermissionDisplay(int userId)
{
    var effectivePermissions = PermissionRepository.GetUserEffectivePermissions(userId);
    var forms = FormRepository.GetPermissionControlledForms();
    var permissionDisplay = new List<PermissionDisplayModel>();

    foreach (var form in forms)
    {
        var formPermissions = effectivePermissions
            .Where(p => p.FormName == form.FormName)
            .ToList();

        var displayModel = new PermissionDisplayModel
        {
            FormName = form.FormName,
            DisplayName = form.DisplayName,
            Category = form.Category,
            CanRead = GetPermissionValue(formPermissions, "Read"),
            CanNew = GetPermissionValue(formPermissions, "New"),
            CanEdit = GetPermissionValue(formPermissions, "Edit"),
            CanDelete = GetPermissionValue(formPermissions, "Delete"),
            ReadSource = GetPermissionSource(formPermissions, "Read"),
            NewSource = GetPermissionSource(formPermissions, "New"),
            EditSource = GetPermissionSource(formPermissions, "Edit"),
            DeleteSource = GetPermissionSource(formPermissions, "Delete"),
            HasOverrides = formPermissions.Any(p => p.Source == "UserOverride"),
            HasRestrictions = formPermissions.Any(p => p.Source == "GlobalRestriction" && !p.IsGranted)
        };

        permissionDisplay.Add(displayModel);
    }

    return permissionDisplay.OrderBy(p => p.Category).ThenBy(p => p.DisplayName).ToList();
}

private bool GetPermissionValue(List<EffectivePermissionModel> permissions, string permissionType)
{
    return permissions.FirstOrDefault(p => p.PermissionName == permissionType)?.IsGranted ?? false;
}

private string GetPermissionSource(List<EffectivePermissionModel> permissions, string permissionType)
{
    var permission = permissions.FirstOrDefault(p => p.PermissionName == permissionType);
    return permission?.Source ?? "None";
}
```

#### Grid Configuration
```csharp
private void ConfigurePermissionGrid()
{
    try
    {
        viewUserPermissions.Columns.Clear();

        // Form information columns
        var colFormName = viewUserPermissions.Columns.AddField("DisplayName");
        colFormName.Caption = "Form";
        colFormName.Width = 150;
        colFormName.VisibleIndex = 0;

        var colCategory = viewUserPermissions.Columns.AddField("Category");
        colCategory.Caption = "Category";
        colCategory.Width = 100;
        colCategory.VisibleIndex = 1;

        // Permission columns with custom appearance
        var colRead = viewUserPermissions.Columns.AddField("CanRead");
        colRead.Caption = "Read";
        colRead.Width = 60;
        colRead.VisibleIndex = 2;
        ConfigurePermissionColumn(colRead);

        var colNew = viewUserPermissions.Columns.AddField("CanNew");
        colNew.Caption = "New";
        colNew.Width = 60;
        colNew.VisibleIndex = 3;
        ConfigurePermissionColumn(colNew);

        var colEdit = viewUserPermissions.Columns.AddField("CanEdit");
        colEdit.Caption = "Edit";
        colEdit.Width = 60;
        colEdit.VisibleIndex = 4;
        ConfigurePermissionColumn(colEdit);

        var colDelete = viewUserPermissions.Columns.AddField("CanDelete");
        colDelete.Caption = "Delete";
        colDelete.Width = 60;
        colDelete.VisibleIndex = 5;
        ConfigurePermissionColumn(colDelete);

        // Status indicators
        var colOverrides = viewUserPermissions.Columns.AddField("HasOverrides");
        colOverrides.Caption = "Overrides";
        colOverrides.Width = 80;
        colOverrides.VisibleIndex = 6;

        var colRestrictions = viewUserPermissions.Columns.AddField("HasRestrictions");
        colRestrictions.Caption = "Restrictions";
        colRestrictions.Width = 80;
        colRestrictions.VisibleIndex = 7;

        // Configure row appearance based on permission sources
        viewUserPermissions.RowCellStyle += ViewUserPermissions_RowCellStyle;
        viewUserPermissions.CustomDrawCell += ViewUserPermissions_CustomDrawCell;

        viewUserPermissions.BestFitColumns();
    }
    catch (Exception ex)
    {
        Debug.WriteLine($"Error configuring permission grid: {ex.Message}");
    }
}

private void ConfigurePermissionColumn(DevExpress.XtraGrid.Columns.GridColumn column)
{
    column.UnboundType = DevExpress.Data.UnboundColumnType.Boolean;
    column.OptionsColumn.AllowEdit = false;
    column.OptionsColumn.AllowFocus = false;
}

private void ViewUserPermissions_RowCellStyle(object sender, DevExpress.XtraGrid.Views.Grid.RowCellStyleEventArgs e)
{
    try
    {
        if (e.RowHandle >= 0 && _userPermissions != null && e.RowHandle < _userPermissions.Count)
        {
            var permission = _userPermissions[e.RowHandle];
            
            // Color code based on permission source
            if (permission.HasRestrictions)
            {
                e.Appearance.BackColor = Color.LightCoral; // Red for restrictions
            }
            else if (permission.HasOverrides)
            {
                e.Appearance.BackColor = Color.LightBlue; // Blue for overrides
            }
            else
            {
                e.Appearance.BackColor = Color.LightGreen; // Green for role-based
            }
        }
    }
    catch (Exception ex)
    {
        Debug.WriteLine($"Error styling permission grid row: {ex.Message}");
    }
}
```

#### Permission Management Integration
```csharp
private void btnManagePermissions_Click(object sender, EventArgs e)
{
    try
    {
        // Check if current user can manage permissions
        var currentUserId = UserManager.Instance.CurrentUser.UserId;
        
        if (!PermissionService.Instance.HasPermission(currentUserId, 
            PermissionConstants.FormNames.PermissionManagementForm, 
            PermissionConstants.PermissionTypes.Read))
        {
            MessageBox.Show("You do not have permission to manage permissions.", 
                "Access Denied", MessageBoxButtons.OK, MessageBoxIcon.Warning);
            return;
        }

        // Open PermissionManagementForm for this user
        var permissionForm = new PermissionManagementForm(_currentUser?.UserId ?? 0);
        
        if (permissionForm.ShowDialog() == DialogResult.OK)
        {
            // Refresh permissions after changes
            LoadUserPermissions();
        }
    }
    catch (Exception ex)
    {
        Debug.WriteLine($"Error opening permission management: {ex.Message}");
        MessageBox.Show("Error opening permission management. Please try again.", 
            "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
    }
}

private void UpdatePermissionSummary()
{
    try
    {
        if (_userPermissions == null || _userPermissions.Count == 0)
        {
            lblPermissionSummary.Text = "No permissions loaded";
            return;
        }

        var totalForms = _userPermissions.Count;
        var accessibleForms = _userPermissions.Count(p => p.CanRead);
        var editableForms = _userPermissions.Count(p => p.CanEdit);
        var formsWithOverrides = _userPermissions.Count(p => p.HasOverrides);
        var formsWithRestrictions = _userPermissions.Count(p => p.HasRestrictions);

        lblPermissionSummary.Text = $"Access: {accessibleForms}/{totalForms} forms | " +
                                   $"Editable: {editableForms} | " +
                                   $"Overrides: {formsWithOverrides} | " +
                                   $"Restrictions: {formsWithRestrictions}";
    }
    catch (Exception ex)
    {
        Debug.WriteLine($"Error updating permission summary: {ex.Message}");
        lblPermissionSummary.Text = "Error loading summary";
    }
}
```

#### Form Lifecycle Integration
```csharp
// Modify existing UserMasterForm_Load method
private void UserMasterForm_Load(object sender, EventArgs e)
{
    try
    {
        // Check form access permission
        if (!PermissionService.Instance.HasPermission(
            UserManager.Instance.CurrentUser.UserId, 
            PermissionConstants.FormNames.UserMasterForm, 
            PermissionConstants.PermissionTypes.Read))
        {
            MessageBox.Show("Access denied to User Management.", "Permission Error");
            this.Close();
            return;
        }

        // Existing initialization code...

        // Load permissions tab data
        LoadUserPermissions();

        // Existing code...
    }
    catch (Exception ex)
    {
        // Existing error handling...
    }
}

// Modify existing SetButtonStates method to include permission checks
private void SetButtonStates()
{
    try
    {
        var currentUserId = UserManager.Instance.CurrentUser.UserId;

        // Existing logic...

        // Add permission-based button enabling
        if (_isViewMode)
        {
            BarButtonItemSave.Enabled = false;
            BarButtonItemCancel.Enabled = false;

            if (BarButtonItemDelete != null)
            {
                BarButtonItemDelete.Enabled = !_isNewMode && _currentUser?.UserId > 0 &&
                    PermissionService.Instance.HasPermission(currentUserId, 
                        PermissionConstants.FormNames.UserMasterForm, 
                        PermissionConstants.PermissionTypes.Delete);
            }
        }
        else
        {
            BarButtonItemSave.Enabled = _isDirty &&
                PermissionService.Instance.HasPermission(currentUserId, 
                    PermissionConstants.FormNames.UserMasterForm, 
                    PermissionConstants.PermissionTypes.Edit);
            BarButtonItemCancel.Enabled = _isDirty;
        }

        BarButtonItemNew.Enabled =
            PermissionService.Instance.HasPermission(currentUserId, 
                PermissionConstants.FormNames.UserMasterForm, 
                PermissionConstants.PermissionTypes.New);

        // Update manage permissions button
        btnManagePermissions.Enabled = _currentUser?.UserId > 0 &&
            PermissionService.Instance.HasPermission(currentUserId,
                PermissionConstants.FormNames.PermissionManagementForm,
                PermissionConstants.PermissionTypes.Read);

        // Existing navigation button logic...
    }
    catch (Exception ex)
    {
        // Existing error handling...
    }
}

// Add method to refresh permissions when user changes
private void OnUserChanged()
{
    try
    {
        LoadUserPermissions();
        SetButtonStates();
    }
    catch (Exception ex)
    {
        Debug.WriteLine($"Error refreshing user permissions: {ex.Message}");
    }
}
```

## Acceptance Criteria

### UI Replacement
- [ ] Existing permission checkboxes completely removed
- [ ] New permission grid displays all form permissions
- [ ] Grid shows effective permissions with source attribution
- [ ] Visual indicators for overrides and restrictions
- [ ] Manage permissions button functional

### Data Display
- [ ] All permission-controlled forms shown in grid
- [ ] Permission sources clearly indicated (Role, Override, Restriction)
- [ ] Color coding for different permission types
- [ ] Permission summary shows accurate counts
- [ ] Grid performance acceptable with all forms

### Integration
- [ ] Seamless integration with existing form lifecycle
- [ ] Permission checks applied to form buttons
- [ ] Manage permissions button respects user permissions
- [ ] Grid refreshes when user data changes
- [ ] No breaking changes to existing functionality

### User Experience
- [ ] Clear visual distinction between permission sources
- [ ] Intuitive grid layout and column organization
- [ ] Helpful summary information displayed
- [ ] Responsive grid performance
- [ ] Consistent with ProManage UI patterns

## Integration Points

### Existing System
- Maintains existing UserMasterForm tab structure
- Uses current form lifecycle and validation patterns
- Integrates with existing button state management
- Compatible with current user loading and saving

### Future Tasks
- Provides foundation for Task 09 (PermissionManagementForm)
- Supports Task 12 and 13 (Form integration tasks)
- Establishes pattern for permission display in other forms
- Enables comprehensive permission management workflow

## Testing Requirements

### UI Testing
```csharp
[Test]
public void LoadUserPermissions_ValidUser_PopulatesGrid()
{
    // Test that grid loads correctly with user permissions
}

[Test]
public void ManagePermissionsButton_UnauthorizedUser_Disabled()
{
    // Test that button is disabled for users without permission
}
```

### Integration Testing
- Test with different user roles and permissions
- Validate grid performance with all forms
- Test permission summary accuracy
- Verify color coding and visual indicators

### User Experience Testing
- Test grid responsiveness and performance
- Validate visual clarity of permission sources
- Test manage permissions workflow
- Verify integration with existing form patterns

## Implementation Notes

### Performance Considerations
- Efficient loading of permission data
- Optimized grid configuration and rendering
- Minimal impact on form load time
- Cached permission data where appropriate

### Visual Design
- Consistent with existing ProManage UI patterns
- Clear visual hierarchy and information display
- Appropriate use of color coding
- Professional appearance and layout

### Maintainability
- Clean separation of permission display logic
- Reusable permission display components
- Clear error handling and logging
- Extensible design for future enhancements

## Files to Create/Modify

### Modified Files (2 files)
- `UserMasterForm.cs` (MODIFY - add permission grid functionality)
- `UserMasterForm.Designer.cs` (MODIFY - replace permission controls)

## Dependencies
- Task 05: Permission Service and Caching (REQUIRED)
- Task 06: Permission Helpers and Utilities (REQUIRED)
- Existing UserMasterForm implementation

## Estimated Effort
- 4-6 hours for designer changes and grid setup
- 6-8 hours for permission display logic
- 2-4 hours for integration with existing form lifecycle
- 3-4 hours for testing and refinement
