# Task 04: Permission Models and Repository Implementation

## Objective
Create the foundational data layer for the RBAC system including models, DTOs, repository classes, and SQL queries following ProManage's established patterns.

## Prerequisites
- Task 01 (RBAC Database Schema Setup) completed
- Task 02 (Default Data Population) completed  
- Task 03 (User Migration) completed
- Database contains populated RBAC tables with test data

## Scope
Implement the complete data access layer for permissions:
1. Create permission-related models and DTOs
2. Implement repository classes with CRUD operations
3. <PERSON>reate optimized SQL queries for permission resolution
4. Establish database connection patterns
5. Implement data validation and error handling

## Deliverables

### 1. Permission Models
**Folder**: `Modules/Models/Permissions/`

#### Core Models (4 files)

**File**: `RoleModel.cs`
```csharp
public class RoleModel
{
    public int RoleId { get; set; }
    public string RoleName { get; set; }
    public string Description { get; set; }
    public bool IsActive { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
}
```

**File**: `FormModel.cs`
```csharp
public class FormModel
{
    public int FormId { get; set; }
    public string FormName { get; set; }
    public string DisplayName { get; set; }
    public string Category { get; set; }
    public bool RequiresPermissions { get; set; }
    public bool IsActive { get; set; }
    public DateTime CreatedAt { get; set; }
}
```

**File**: `PermissionTypeModel.cs`
```csharp
public class PermissionTypeModel
{
    public int PermissionId { get; set; }
    public string PermissionName { get; set; }
    public string Description { get; set; }
    public bool IsActive { get; set; }
    public DateTime CreatedAt { get; set; }
}
```

**File**: `EffectivePermissionModel.cs`
```csharp
public class EffectivePermissionModel
{
    public string FormName { get; set; }
    public string PermissionName { get; set; }
    public bool IsGranted { get; set; }
    public string Source { get; set; } // "Role", "UserOverride", "GlobalRestriction"
    public string RoleName { get; set; }
    public int Level { get; set; } // 1=Global, 2=UserOverride, 3=Role
}
```

### 2. Data Transfer Objects (DTOs)
**Folder**: `Modules/Models/Permissions/DTOs/`

**File**: `UserPermissionSummaryDto.cs`
```csharp
public class UserPermissionSummaryDto
{
    public int UserId { get; set; }
    public string Username { get; set; }
    public string RoleName { get; set; }
    public Dictionary<string, Dictionary<string, bool>> FormPermissions { get; set; }
    public List<string> VisibleForms { get; set; }
    public bool HasGlobalRestrictions { get; set; }
}
```

**File**: `PermissionMatrixDto.cs`
```csharp
public class PermissionMatrixDto
{
    public string FormName { get; set; }
    public string DisplayName { get; set; }
    public string Category { get; set; }
    public Dictionary<string, bool> Permissions { get; set; } // Permission -> IsGranted
    public string Source { get; set; }
}
```

### 3. Repository Implementation
**Folder**: `Modules/Data/Permissions/`

#### Main Repository Class
**File**: `PermissionRepository.cs`

**Key Methods**:
```csharp
public static class PermissionRepository
{
    // Core permission checking
    public static bool HasUserPermission(int userId, string formName, string permissionType);
    public static List<EffectivePermissionModel> GetUserEffectivePermissions(int userId);
    public static List<string> GetUserVisibleForms(int userId);
    
    // Permission management
    public static bool SetRolePermission(int roleId, string formName, string permissionType, bool isGranted);
    public static bool SetUserPermission(int userId, string formName, string permissionType, bool isGranted);
    public static bool SetUserManagementPermission(int userId, string permissionType, bool isGranted);
    
    // Data retrieval
    public static List<RoleModel> GetAllRoles();
    public static List<FormModel> GetAllForms();
    public static List<PermissionTypeModel> GetAllPermissionTypes();
    public static UserPermissionSummaryDto GetUserPermissionSummary(int userId);
}
```

#### Supporting Repository Classes
**File**: `RoleRepository.cs`
```csharp
public static class RoleRepository
{
    public static List<RoleModel> GetActiveRoles();
    public static RoleModel GetRoleById(int roleId);
    public static RoleModel GetRoleByName(string roleName);
    public static bool CreateRole(RoleModel role);
    public static bool UpdateRole(RoleModel role);
    public static bool DeleteRole(int roleId);
}
```

**File**: `FormRepository.cs`
```csharp
public static class FormRepository
{
    public static List<FormModel> GetPermissionControlledForms();
    public static FormModel GetFormByName(string formName);
    public static bool RegisterForm(FormModel form);
    public static bool UpdateForm(FormModel form);
}
```

### 4. SQL Queries Implementation
**Folder**: `Modules/Procedures/Permissions/`

#### Core Permission Resolution Queries
**File**: `PermissionResolution.sql`

**Key Queries**:
```sql
-- [HasUserPermission] --
-- Fast permission check for single user/form/permission combination
-- Uses 3-layer hierarchy: UserManagement -> UserOverride -> Role

-- [GetUserEffectivePermissions] --  
-- Complete permission matrix for a user showing all forms and permissions
-- Includes source attribution (Role, UserOverride, GlobalRestriction)

-- [GetUserVisibleForms] --
-- List of forms that should appear in ribbon for user
-- Based on role Read permissions only

-- [GetPermissionHierarchy] --
-- Detailed view of permission resolution with all 3 levels
-- Used for debugging and permission management UI
```

#### CRUD Operations
**File**: `PermissionCRUD.sql`

**Key Operations**:
```sql
-- [SetRolePermission] --
-- [SetUserPermission] --
-- [SetUserManagementPermission] --
-- [GetRolePermissions] --
-- [GetUserPermissions] --
-- [DeleteUserPermissions] --
-- [BulkUpdateRolePermissions] --
```

#### Administrative Queries
**File**: `PermissionManagement.sql`

**Key Queries**:
```sql
-- [GetPermissionMatrix] --
-- [GetRolePermissionSummary] --
-- [GetUserPermissionAudit] --
-- [ValidatePermissionIntegrity] --
-- [GetPermissionStatistics] --
```

### 5. Database Connection Integration

#### Connection Manager Integration
**File**: `Modules/Data/Permissions/PermissionConnectionManager.cs`

```csharp
public static class PermissionConnectionManager
{
    public static string GetConnectionString()
    {
        return DatabaseConnectionManager.GetConnectionString();
    }
    
    public static NpgsqlConnection GetConnection()
    {
        return DatabaseConnectionManager.GetConnection();
    }
    
    public static async Task<T> ExecuteQueryAsync<T>(string query, object parameters = null)
    {
        // Use existing connection patterns
        // Implement proper error handling
        // Support both sync and async operations
    }
}
```

### 6. SQL Query Constants
**File**: `Modules/Helpers/Permissions/PermissionQueries.cs`

```csharp
public static class PermissionQueries
{
    // Core permission checking
    public const string HasUserPermission = "HasUserPermission";
    public const string GetUserEffectivePermissions = "GetUserEffectivePermissions";
    public const string GetUserVisibleForms = "GetUserVisibleForms";
    
    // Permission management
    public const string SetRolePermission = "SetRolePermission";
    public const string SetUserPermission = "SetUserPermission";
    public const string SetUserManagementPermission = "SetUserManagementPermission";
    
    // Data retrieval
    public const string GetAllRoles = "GetAllRoles";
    public const string GetAllForms = "GetAllForms";
    public const string GetAllPermissionTypes = "GetAllPermissionTypes";
}
```

## Acceptance Criteria

### Model Implementation
- [ ] All 4 core models created with proper properties
- [ ] DTOs created for complex data transfer scenarios
- [ ] Models follow ProManage naming conventions
- [ ] Proper data annotations for validation

### Repository Implementation
- [ ] PermissionRepository with all core methods
- [ ] Supporting repositories for roles and forms
- [ ] Proper error handling and logging
- [ ] Integration with existing DatabaseConnectionManager

### SQL Query Implementation
- [ ] All permission resolution queries optimized
- [ ] CRUD operations for all permission types
- [ ] Administrative and audit queries
- [ ] Proper parameterization and SQL injection prevention

### Database Integration
- [ ] Connection management follows existing patterns
- [ ] Proper transaction handling
- [ ] Error handling and logging
- [ ] Performance optimization with indexes

### Code Quality
- [ ] All files under 500 lines as per guidelines
- [ ] Proper namespace organization
- [ ] Comprehensive error handling
- [ ] XML documentation for public methods

## Integration Points

### Existing System
- Uses `DatabaseConnectionManager` for connections
- Follows existing repository patterns
- Integrates with current SQL file organization
- Compatible with existing error handling

### Future Tasks
- Provides foundation for Task 05 (Permission Service)
- Enables Task 08 (UserMasterForm integration)
- Supports Task 09 (PermissionManagementForm)
- Establishes data layer for all permission operations

## Testing Requirements

### Unit Testing
```csharp
// Test permission resolution logic
[Test]
public void HasUserPermission_AdminUser_ReturnsTrue()
{
    var result = PermissionRepository.HasUserPermission(1, "EstimateForm", "Read");
    Assert.IsTrue(result);
}

// Test data retrieval
[Test]
public void GetUserEffectivePermissions_ValidUser_ReturnsPermissions()
{
    var permissions = PermissionRepository.GetUserEffectivePermissions(1);
    Assert.IsNotNull(permissions);
    Assert.IsTrue(permissions.Count > 0);
}
```

### Integration Testing
- Test database connectivity
- Validate SQL query execution
- Test transaction handling
- Verify error scenarios

### Performance Testing
- Test permission lookup performance
- Validate index usage
- Test with large datasets
- Monitor query execution times

## Implementation Notes

### Performance Optimization
- Use composite indexes for fast lookups
- Implement query result caching where appropriate
- Optimize SQL queries for minimal joins
- Use parameterized queries for security

### Error Handling
- Comprehensive exception handling
- Proper logging of database errors
- Graceful degradation for missing data
- Clear error messages for debugging

### Security Considerations
- Parameterized queries prevent SQL injection
- Proper validation of input parameters
- Secure handling of permission data
- Audit trail for permission changes

## Files to Create/Modify

### New Files (12 files)
- `Modules/Models/Permissions/RoleModel.cs`
- `Modules/Models/Permissions/FormModel.cs`
- `Modules/Models/Permissions/PermissionTypeModel.cs`
- `Modules/Models/Permissions/EffectivePermissionModel.cs`
- `Modules/Models/Permissions/DTOs/UserPermissionSummaryDto.cs`
- `Modules/Models/Permissions/DTOs/PermissionMatrixDto.cs`
- `Modules/Data/Permissions/PermissionRepository.cs`
- `Modules/Data/Permissions/RoleRepository.cs`
- `Modules/Data/Permissions/FormRepository.cs`
- `Modules/Procedures/Permissions/PermissionResolution.sql`
- `Modules/Procedures/Permissions/PermissionCRUD.sql`
- `Modules/Helpers/Permissions/PermissionQueries.cs`

## Dependencies
- Task 01: RBAC Database Schema Setup (REQUIRED)
- Task 02: Default Data Population (REQUIRED)
- Task 03: User Migration (REQUIRED)

## Estimated Effort
- 6-8 hours for models and DTOs
- 8-10 hours for repository implementation
- 6-8 hours for SQL queries
- 4-6 hours for testing and validation
