-- =====================================================
-- ProManage RBAC System - Add Validation Constraints
-- =====================================================
--
-- This script adds validation constraints to the existing 3 tables
-- to ensure form names and permission types match the hardcoded
-- application constants.
--
-- Run this script AFTER removing the unnecessary tables to add
-- proper validation to the remaining 3-table structure.
--
-- =====================================================

-- Start transaction for atomic operation
BEGIN;

-- Set error handling
\set ON_ERROR_STOP on

-- =====================================================
-- SECTION 1: VALIDATION CONSTRAINTS
-- =====================================================

-- Add constraint for valid form names in role_perms table
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.table_constraints 
                   WHERE constraint_name = 'chk_role_perms_form_name') THEN
        ALTER TABLE role_perms ADD CONSTRAINT chk_role_perms_form_name
            CHECK (form_name IN ('EstimateForm', 'DatabaseForm', 'ParametersForm', 'RoleMasterForm', 'SQLQueryForm', 'UserManagementListForm', 'UserMasterForm'));
        RAISE NOTICE 'Added constraint chk_role_perms_form_name.';
    ELSE
        RAISE NOTICE 'Constraint chk_role_perms_form_name already exists.';
    END IF;
EXCEPTION
    WHEN check_violation THEN
        RAISE EXCEPTION 'Cannot add form_name constraint - existing data contains invalid form names. Please check role_perms table data.';
END $$;

-- Add constraint for valid permission names in role_perms table
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.table_constraints 
                   WHERE constraint_name = 'chk_role_perms_permission_name') THEN
        ALTER TABLE role_perms ADD CONSTRAINT chk_role_perms_permission_name
            CHECK (permission_name IN ('Read', 'New', 'Edit', 'Delete'));
        RAISE NOTICE 'Added constraint chk_role_perms_permission_name.';
    ELSE
        RAISE NOTICE 'Constraint chk_role_perms_permission_name already exists.';
    END IF;
EXCEPTION
    WHEN check_violation THEN
        RAISE EXCEPTION 'Cannot add permission_name constraint - existing data contains invalid permission names. Please check role_perms table data.';
END $$;

-- Add constraint for valid form names in user_perms table
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.table_constraints 
                   WHERE constraint_name = 'chk_user_perms_form_name') THEN
        ALTER TABLE user_perms ADD CONSTRAINT chk_user_perms_form_name
            CHECK (form_name IN ('EstimateForm', 'DatabaseForm', 'ParametersForm', 'RoleMasterForm', 'SQLQueryForm', 'UserManagementListForm', 'UserMasterForm'));
        RAISE NOTICE 'Added constraint chk_user_perms_form_name.';
    ELSE
        RAISE NOTICE 'Constraint chk_user_perms_form_name already exists.';
    END IF;
EXCEPTION
    WHEN check_violation THEN
        RAISE EXCEPTION 'Cannot add form_name constraint - existing data contains invalid form names. Please check user_perms table data.';
END $$;

-- Add constraint for valid permission names in user_perms table
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.table_constraints 
                   WHERE constraint_name = 'chk_user_perms_permission_name') THEN
        ALTER TABLE user_perms ADD CONSTRAINT chk_user_perms_permission_name
            CHECK (permission_name IN ('Read', 'New', 'Edit', 'Delete'));
        RAISE NOTICE 'Added constraint chk_user_perms_permission_name.';
    ELSE
        RAISE NOTICE 'Constraint chk_user_perms_permission_name already exists.';
    END IF;
EXCEPTION
    WHEN check_violation THEN
        RAISE EXCEPTION 'Cannot add permission_name constraint - existing data contains invalid permission names. Please check user_perms table data.';
END $$;

-- =====================================================
-- SECTION 2: DATA VALIDATION
-- =====================================================

-- Check for invalid form names in existing data
DO $$
DECLARE
    invalid_forms_role_perms INTEGER;
    invalid_forms_user_perms INTEGER;
    invalid_perms_role_perms INTEGER;
    invalid_perms_user_perms INTEGER;
    valid_forms TEXT[] := ARRAY['EstimateForm', 'DatabaseForm', 'ParametersForm', 'RoleMasterForm', 'SQLQueryForm', 'UserManagementListForm', 'UserMasterForm'];
    valid_permissions TEXT[] := ARRAY['Read', 'New', 'Edit', 'Delete'];
BEGIN
    RAISE NOTICE 'Validating existing data...';
    
    -- Check role_perms for invalid form names
    SELECT COUNT(*) INTO invalid_forms_role_perms
    FROM role_perms 
    WHERE form_name NOT = ANY(valid_forms);
    
    -- Check user_perms for invalid form names
    SELECT COUNT(*) INTO invalid_forms_user_perms
    FROM user_perms 
    WHERE form_name NOT = ANY(valid_forms);
    
    -- Check role_perms for invalid permission names
    SELECT COUNT(*) INTO invalid_perms_role_perms
    FROM role_perms 
    WHERE permission_name NOT = ANY(valid_permissions);
    
    -- Check user_perms for invalid permission names
    SELECT COUNT(*) INTO invalid_perms_user_perms
    FROM user_perms 
    WHERE permission_name NOT = ANY(valid_permissions);
    
    -- Report results
    IF invalid_forms_role_perms > 0 THEN
        RAISE WARNING 'Found % invalid form names in role_perms table', invalid_forms_role_perms;
    ELSE
        RAISE NOTICE 'All form names in role_perms are valid ✓';
    END IF;
    
    IF invalid_forms_user_perms > 0 THEN
        RAISE WARNING 'Found % invalid form names in user_perms table', invalid_forms_user_perms;
    ELSE
        RAISE NOTICE 'All form names in user_perms are valid ✓';
    END IF;
    
    IF invalid_perms_role_perms > 0 THEN
        RAISE WARNING 'Found % invalid permission names in role_perms table', invalid_perms_role_perms;
    ELSE
        RAISE NOTICE 'All permission names in role_perms are valid ✓';
    END IF;
    
    IF invalid_perms_user_perms > 0 THEN
        RAISE WARNING 'Found % invalid permission names in user_perms table', invalid_perms_user_perms;
    ELSE
        RAISE NOTICE 'All permission names in user_perms are valid ✓';
    END IF;
END $$;

-- =====================================================
-- SECTION 3: VERIFICATION
-- =====================================================

-- Verify all constraints are in place
DO $$
DECLARE
    constraint_count INTEGER;
    expected_constraints TEXT[] := ARRAY[
        'chk_role_perms_form_name',
        'chk_role_perms_permission_name', 
        'chk_user_perms_form_name',
        'chk_user_perms_permission_name',
        'chk_user_perms_type'
    ];
    constraint_name TEXT;
BEGIN
    RAISE NOTICE 'Verifying validation constraints...';
    
    FOREACH constraint_name IN ARRAY expected_constraints LOOP
        SELECT COUNT(*) INTO constraint_count
        FROM information_schema.table_constraints
        WHERE constraint_name = constraint_name;
        
        IF constraint_count = 0 THEN
            RAISE WARNING 'Constraint % is missing!', constraint_name;
        ELSE
            RAISE NOTICE 'Constraint % exists ✓', constraint_name;
        END IF;
    END LOOP;
END $$;

-- Final summary
SELECT
    'RBAC Validation Summary' as component,
    (SELECT COUNT(*) FROM information_schema.table_constraints 
     WHERE constraint_name LIKE 'chk_role_perms_%' OR constraint_name LIKE 'chk_user_perms_%') as validation_constraints_count,
    (SELECT COUNT(*) FROM role_perms) as role_permissions_count,
    (SELECT COUNT(*) FROM user_perms) as user_permissions_count;

-- =====================================================
-- SECTION 4: COMPLETION
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE '=====================================================';
    RAISE NOTICE 'RBAC Validation Constraints Added Successfully!';
    RAISE NOTICE '=====================================================';
    RAISE NOTICE 'Added validation constraints for:';
    RAISE NOTICE '- Form names (7 valid forms)';
    RAISE NOTICE '- Permission types (4 valid permissions)';
    RAISE NOTICE '- Override types (user_management, form_specific)';
    RAISE NOTICE '';
    RAISE NOTICE 'The 3-table RBAC system now has proper validation';
    RAISE NOTICE 'and will reject invalid form/permission combinations.';
    RAISE NOTICE '=====================================================';
END $$;

-- Commit the transaction
COMMIT;

-- Final success message
\echo 'RBAC validation constraints added successfully!'
\echo 'The 3-table system now has proper data validation.'
