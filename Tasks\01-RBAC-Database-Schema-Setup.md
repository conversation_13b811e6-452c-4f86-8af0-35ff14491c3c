# Task 01: RBAC Database Schema Setup

## Objective
Create the complete Role-Based Access Control (RBAC) database schema with 5 optimized tables, proper indexes, constraints, and relationships following PostgreSQL best practices.

## Prerequisites
- PostgreSQL database connection established
- Database backup created (recommended)
- Review of `docs/Users and Role management.md` sections 3 and 13
- Access to PostgreSQL tool for schema creation

## Scope
Implement the 5-table RBAC schema designed for scalability and performance:
1. `roles` - Role definitions and metadata
2. `forms` - Dynamic form registry for permission control
3. `permission_types` - Configurable permission types
4. `role_perms` - Role-based default permissions (Level 3)
5. `user_perms` - User overrides and global restrictions (Levels 1 & 2)

## Deliverables

### 1. SQL Schema Script
**File**: `Modules/Procedures/Permissions/01-Schema-Creation.sql`

**Content Requirements**:
- Transaction-wrapped schema creation
- All 5 tables with proper data types
- Primary keys, foreign keys, and constraints
- Optimized indexes for performance
- Comprehensive error handling
- Rollback capability on failure

### 2. Table Specifications

#### roles Table
```sql
- role_id (SERIAL PRIMARY KEY)
- role_name (VARCHAR(50) UNIQUE NOT NULL)
- description (TEXT)
- is_active (BOOLEAN DEFAULT true)
- created_at (TIMESTAMP DEFAULT CURRENT_TIMESTAMP)
- updated_at (TIMESTAMP DEFAULT CURRENT_TIMESTAMP)
```

#### forms Table
```sql
- form_id (SERIAL PRIMARY KEY)
- form_name (VARCHAR(100) UNIQUE NOT NULL)
- display_name (VARCHAR(150) NOT NULL)
- category (VARCHAR(50) DEFAULT 'business')
- requires_permissions (BOOLEAN DEFAULT true)
- is_active (BOOLEAN DEFAULT true)
- created_at (TIMESTAMP DEFAULT CURRENT_TIMESTAMP)
```

#### permission_types Table
```sql
- permission_id (SERIAL PRIMARY KEY)
- permission_name (VARCHAR(50) UNIQUE NOT NULL)
- description (TEXT)
- is_active (BOOLEAN DEFAULT true)
- created_at (TIMESTAMP DEFAULT CURRENT_TIMESTAMP)
```

#### role_perms Table
```sql
- perm_id (SERIAL PRIMARY KEY)
- role_id (INTEGER REFERENCES roles(role_id) ON DELETE CASCADE)
- form_name (VARCHAR(100) NOT NULL)
- permission_name (VARCHAR(50) NOT NULL)
- is_granted (BOOLEAN NOT NULL)
- created_at (TIMESTAMP DEFAULT CURRENT_TIMESTAMP)
- UNIQUE(role_id, form_name, permission_name)
```

#### user_perms Table
```sql
- perm_id (SERIAL PRIMARY KEY)
- user_id (INTEGER REFERENCES users(user_id) ON DELETE CASCADE)
- override_type (VARCHAR(20) NOT NULL) -- 'user_management' or 'form_specific'
- form_name (VARCHAR(100) NOT NULL)
- permission_name (VARCHAR(50) NOT NULL)
- is_granted (BOOLEAN NOT NULL)
- created_at (TIMESTAMP DEFAULT CURRENT_TIMESTAMP)
- UNIQUE(user_id, override_type, form_name, permission_name)
```

### 3. Performance Indexes
```sql
-- Role lookups
CREATE INDEX idx_roles_name ON roles(role_name);
CREATE INDEX idx_roles_active ON roles(is_active);

-- Form lookups
CREATE INDEX idx_forms_name ON forms(form_name);
CREATE INDEX idx_forms_active ON forms(is_active);
CREATE INDEX idx_forms_category ON forms(category);

-- Permission type lookups
CREATE INDEX idx_permission_types_name ON permission_types(permission_name);

-- Role permission lookups (composite for fast resolution)
CREATE INDEX idx_role_perms_lookup ON role_perms(role_id, form_name, permission_name);
CREATE INDEX idx_role_perms_form ON role_perms(form_name);

-- User permission lookups (composite for fast resolution)
CREATE INDEX idx_user_perms_lookup ON user_perms(user_id, override_type, form_name, permission_name);
CREATE INDEX idx_user_perms_user ON user_perms(user_id);
CREATE INDEX idx_user_perms_type ON user_perms(override_type);
```

### 4. Users Table Extension
Add `role_id` column to existing users table:
```sql
-- Add role_id column (nullable initially for migration)
ALTER TABLE users ADD COLUMN role_id INTEGER;
ALTER TABLE users ADD CONSTRAINT fk_users_role 
    FOREIGN KEY (role_id) REFERENCES roles(role_id);
CREATE INDEX idx_users_role ON users(role_id);
```

## Acceptance Criteria

### Database Structure
- [ ] All 5 tables created successfully
- [ ] All primary keys and foreign keys working
- [ ] All unique constraints enforced
- [ ] All indexes created and optimized
- [ ] Users table extended with role_id column

### Data Integrity
- [ ] Foreign key constraints prevent orphaned records
- [ ] Unique constraints prevent duplicate permissions
- [ ] Check constraints validate override_type values
- [ ] Cascade deletes work correctly

### Performance
- [ ] Query execution plans show index usage
- [ ] Permission lookup queries execute under 50ms
- [ ] Bulk operations complete efficiently
- [ ] No table scans on large permission queries

### Error Handling
- [ ] Script handles existing table scenarios gracefully
- [ ] Transaction rollback works on any failure
- [ ] Clear error messages for constraint violations
- [ ] Proper logging of schema creation steps

## Integration Points

### Existing System
- Extends existing `users` table without breaking changes
- Compatible with current `DatabaseConnectionManager`
- Follows existing SQL file organization patterns
- Uses established PostgreSQL connection methods

### Future Tasks
- Provides foundation for Task 02 (Default Data Population)
- Enables Task 04 (Permission Models and Repository)
- Supports all subsequent permission system components

## Testing Requirements

### Schema Validation
```sql
-- Verify table creation
SELECT table_name FROM information_schema.tables 
WHERE table_schema = 'public' AND table_name IN 
('roles', 'forms', 'permission_types', 'role_perms', 'user_perms');

-- Verify indexes
SELECT indexname FROM pg_indexes 
WHERE tablename IN ('roles', 'forms', 'permission_types', 'role_perms', 'user_perms');

-- Verify foreign keys
SELECT constraint_name, table_name, column_name, foreign_table_name, foreign_column_name
FROM information_schema.key_column_usage k
JOIN information_schema.referential_constraints r ON k.constraint_name = r.constraint_name
WHERE k.table_name IN ('role_perms', 'user_perms', 'users');
```

### Performance Testing
- Test permission lookup queries with sample data
- Verify index usage with EXPLAIN ANALYZE
- Test constraint enforcement with invalid data
- Validate transaction rollback scenarios

## Implementation Notes

### Transaction Safety
- Wrap entire schema creation in single transaction
- Use IF NOT EXISTS where appropriate
- Provide clear rollback instructions
- Include verification queries at end

### PostgreSQL Best Practices
- Use SERIAL for auto-incrementing primary keys
- Implement proper foreign key cascading
- Create composite indexes for multi-column lookups
- Use appropriate data types for performance

### Error Prevention
- Check for existing tables before creation
- Validate foreign key references
- Test all constraints with sample data
- Provide clear error messages

## Files to Create/Modify
- `Modules/Procedures/Permissions/01-Schema-Creation.sql` (NEW)
- Database schema (5 new tables + users table modification)

## Dependencies
- None (foundational task)

## Estimated Effort
- 4-6 hours for complete schema design and testing
- Additional 2 hours for performance optimization and validation
