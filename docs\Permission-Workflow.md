# ProManage Permission System Workflow

## Overview
This document explains how the ProManage permission system works from a user and developer perspective.

## System Architecture

### 1. Permission Levels (Simplified 2-Level System)

| Level | Description | Override Power |
|-------|-------------|----------------|
| **Level 1: Role Permissions** | Default permissions assigned to user roles | Base permissions |
| **Level 2: User Overrides** | Individual user permission overrides | Overrides role permissions |

### 2. Permission Resolution Logic

```
For any form and permission type:
1. Check if user has specific override → Use user override value
2. If no user override exists → Use role permission value
3. If no role permission exists → Default to DENY
```

**Example:**
- Role "User" has EstimateForm Edit = TRUE
- User "John" has EstimateForm Edit override = FALSE
- **Result:** <PERSON> cannot edit estimates (user override wins)

## Forms and Permissions

### 1. Forms Configuration (Config File Approach)

**File:** `Modules/Config/FormsConfig.json`
```json
{
  "forms": [
    {
      "formName": "EstimateForm",
      "displayName": "Estimate Management",
      "category": "Business",
      "permissions": ["Read", "New", "Edit", "Delete"]
    },
    {
      "formName": "UserMasterForm", 
      "displayName": "User Management",
      "category": "System",
      "permissions": ["Read", "New", "Edit", "Delete"]
    }
  ]
}
```

**Benefits:**
- Version controlled with code
- Easy to modify during development
- No database queries for form lists
- Simple to understand and maintain

### 2. Permission Types

| Permission | Description | Effect |
|------------|-------------|---------|
| **Read** | View/access the form | Form appears in ribbon menu |
| **New** | Create new records | New button enabled |
| **Edit** | Modify existing records | Edit/Save buttons enabled |
| **Delete** | Remove records | Delete button enabled |

## User Interface Workflow

### 1. Permission Management Form

**Tab 1: User Permissions**
1. Select user from dropdown
2. Grid displays all forms with permission checkboxes
3. Check/uncheck permissions for specific user
4. Save changes → Updates user_permissions table

**Tab 2: Role Permissions** 
1. Select role from dropdown
2. Grid displays all forms with permission checkboxes
3. Check/uncheck permissions for role
4. Save changes → Updates role_permissions table

### 2. UserMasterForm Integration

**Permissions Tab:**
- Displays effective permissions for selected user
- Shows inheritance (from role) vs overrides (user-specific)
- "Manage Permissions" button opens Permission Management Form

**Global Permissions (in General tab):**
- Full system permissions (Create, Read, Update, Delete, Export, Print)
- These are separate from form-specific permissions
- Control overall system access level

## Technical Implementation Flow

### 1. User Login Process
```
1. User logs in → UserManager.Instance.CurrentUser set
2. PermissionService loads user permissions into cache
3. MainFrame filters ribbon based on Read permissions
4. User can only see forms they have Read access to
```

### 2. Form Opening Process
```
1. User clicks ribbon button
2. MainFrame checks Read permission for form
3. If denied → Show "Access Denied" message
4. If allowed → Open form as MDI child
```

### 3. Form Operation Process
```
1. Form loads → Check Read permission
2. Form sets button states based on New/Edit/Delete permissions
3. User attempts operation → Permission checked again
4. If denied → Show error and prevent operation
```

### 4. Permission Change Process
```
1. Admin opens Permission Management Form
2. Admin modifies user/role permissions
3. Changes saved to database
4. PermissionService cache refreshed for affected users
5. Active forms update button states immediately
```

## Database Schema (Simplified)

### Tables Required:
1. **roles** - Role definitions
2. **role_permissions** - Default permissions for each role
3. **user_permissions** - User-specific permission overrides

### Key Relationships:
- users.role_id → roles.role_id
- role_permissions.role_id → roles.role_id  
- user_permissions.user_id → users.user_id

## Permission Scenarios

### Scenario 1: Standard User
- Role: "User" 
- EstimateForm permissions: Read=Yes, New=Yes, Edit=Yes, Delete=No
- No user overrides
- **Result:** Can view, create, edit estimates but cannot delete

### Scenario 2: Restricted User
- Role: "User"
- EstimateForm permissions: Read=Yes, New=Yes, Edit=Yes, Delete=No
- User override: Edit=No
- **Result:** Can view and create estimates but cannot edit or delete

### Scenario 3: Admin User
- Role: "Admin"
- All permissions: Read=Yes, New=Yes, Edit=Yes, Delete=Yes
- No user overrides needed
- **Result:** Full access to all forms and operations

## Security Features

### 1. Form Visibility Control
- Forms only appear in ribbon if user has Read permission
- No way to access hidden forms through UI

### 2. Operation-Level Security
- Each button/operation checked individually
- Permissions verified both on form load and operation attempt

### 3. Cache Security
- Permissions cached for performance
- Cache automatically refreshed when permissions change
- No stale permission data

## Benefits of This Approach

1. **Simple to Understand:** Clear 2-level hierarchy
2. **Easy to Manage:** Intuitive tab-based UI
3. **Flexible:** Role defaults with user overrides
4. **Maintainable:** Config file for forms, simple database schema
5. **Secure:** Multiple permission checks, no bypass possible
6. **Performant:** Cached permissions, minimal database queries
