# ProManage RBAC System - Implementation Plan
## Simple, Practical Permission Management

> **Document Purpose**: This document outlines the practical implementation of a clean, simple RBAC system for ProManage based on your excellent 2-level permission concept. This is what we will actually build.

---

## 1. System Overview

### 1.1 Your Core Concept (What We're Building)
- **2-Level Permission System**: Role permissions + User overrides
- **Tab-Based UI**: Clean permission management interface
- **Config File Approach**: Forms defined in JSON, not database
- **Simple Override Logic**: User permissions override role permissions when set

### 1.2 Permission Resolution Logic
```
For any form and permission type:
1. Check if user has specific override → Use user override value
2. If no user override exists → Use role permission value  
3. If no role permission exists → Default to DENY
```

---

## 2. Database Schema (Simple & Clean)

### 2.1 Required Tables (3 Tables Only)

#### roles Table
```sql
CREATE TABLE roles (
    role_id SERIAL PRIMARY KEY,
    role_name VA<PERSON>HA<PERSON>(50) UNIQUE NOT NULL,
    description TEXT,
    is_active BOOLEAN DEFAULT true,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### role_permissions Table
```sql
CREATE TABLE role_permissions (
    perm_id SERIAL PRIMARY KEY,
    role_id INTEGER REFERENCES roles(role_id),
    form_name VARCHAR(100) NOT NULL,
    permission_type VARCHAR(20) NOT NULL, -- Read, New, Edit, Delete
    is_granted BOOLEAN DEFAULT false,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(role_id, form_name, permission_type)
);
```

#### user_permissions Table (User Overrides)
```sql
CREATE TABLE user_permissions (
    perm_id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(user_id),
    form_name VARCHAR(100) NOT NULL,
    permission_type VARCHAR(20) NOT NULL,
    is_granted BOOLEAN DEFAULT false,
    override_reason TEXT,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by INTEGER REFERENCES users(user_id),
    UNIQUE(user_id, form_name, permission_type)
);
```

### 2.2 Update Existing Users Table
```sql
-- Add role_id to existing users table
ALTER TABLE users ADD COLUMN role_id INTEGER REFERENCES roles(role_id);

-- Populate with default roles
INSERT INTO roles (role_name, description) VALUES 
('Administrator', 'Full system access'),
('Manager', 'Management level access'),
('User', 'Standard user access');

-- Map existing users
UPDATE users SET role_id = (
    CASE 
        WHEN LOWER(role) = 'admin' THEN (SELECT role_id FROM roles WHERE role_name = 'Administrator')
        ELSE (SELECT role_id FROM roles WHERE role_name = 'User')
    END
);
```

---

## 3. Forms Configuration (Config File Approach)

### 3.1 Forms Configuration File
**File**: `Modules/Config/FormsConfig.json`
```json
{
  "forms": [
    {
      "formName": "EstimateForm",
      "displayName": "Estimate Management",
      "category": "Business",
      "permissions": ["Read", "New", "Edit", "Delete"]
    },
    {
      "formName": "UserMasterForm",
      "displayName": "User Management", 
      "category": "System",
      "permissions": ["Read", "New", "Edit", "Delete"]
    },
    {
      "formName": "UserManagementListForm",
      "displayName": "User List Management",
      "category": "System", 
      "permissions": ["Read", "New", "Edit", "Delete"]
    },
    {
      "formName": "ParametersForm",
      "displayName": "System Parameters",
      "category": "System",
      "permissions": ["Read", "Edit"]
    },
    {
      "formName": "DatabaseForm",
      "displayName": "Database Configuration",
      "category": "System",
      "permissions": ["Read", "Edit"]
    },
    {
      "formName": "SQLQueryForm", 
      "displayName": "SQL Query Tool",
      "category": "System",
      "permissions": ["Read"]
    }
  ]
}
```

---

## 4. Permission Management UI (Your Tab Design)

### 4.1 Permission Management Form Structure
```
PermissionManagementForm
├── Tab 1: Role Permissions
│   ├── Role Selection Dropdown
│   ├── Forms Grid (Read/New/Edit/Delete columns)
│   └── Save/Cancel Buttons
└── Tab 2: User Permissions  
    ├── User Selection Dropdown
    ├── Forms Grid (shows role vs user override)
    └── Save/Cancel Buttons
```

### 4.2 Tab 1: Role Permissions
- **Role Dropdown**: Select role to manage
- **Permission Grid**: 
  - Rows: Forms from config file
  - Columns: Read, New, Edit, Delete (checkboxes)
  - Save changes to `role_permissions` table

### 4.3 Tab 2: User Permissions
- **User Dropdown**: Select user to manage  
- **Permission Grid**:
  - Rows: Forms from config file
  - Columns: Read, New, Edit, Delete (checkboxes)
  - Visual indicators: Inherited (from role) vs Override (user-specific)
  - Save changes to `user_permissions` table

---

## 5. Core Service Implementation

### 5.1 Permission Service (Simple)
```csharp
public sealed class PermissionService
{
    private static readonly Lazy<PermissionService> _instance =
        new Lazy<PermissionService>(() => new PermissionService());
    
    public static PermissionService Instance => _instance.Value;
    
    public bool HasPermission(int userId, string formName, string permissionType)
    {
        // 1. Check user-specific override first
        var userPermission = GetUserPermission(userId, formName, permissionType);
        if (userPermission.HasValue)
            return userPermission.Value;
            
        // 2. Check role-based permission
        var rolePermission = GetRolePermission(userId, formName, permissionType);
        if (rolePermission.HasValue)
            return rolePermission.Value;
            
        // 3. Default deny
        return false;
    }
    
    public List<string> GetVisibleForms(int userId)
    {
        var visibleForms = new List<string>();
        var forms = FormsConfigService.GetAllForms();
        
        foreach (var form in forms)
        {
            if (HasPermission(userId, form.FormName, "Read"))
            {
                visibleForms.Add(form.FormName);
            }
        }
        
        return visibleForms;
    }
}
```

### 5.2 Forms Config Service
```csharp
public static class FormsConfigService
{
    private static List<FormConfig> _forms;
    
    public static List<FormConfig> GetAllForms()
    {
        if (_forms == null)
        {
            var configPath = Path.Combine(Application.StartupPath, "Modules", "Config", "FormsConfig.json");
            var json = File.ReadAllText(configPath);
            var config = JsonSerializer.Deserialize<FormsConfiguration>(json);
            _forms = config.Forms;
        }
        return _forms;
    }
    
    public static FormConfig GetFormConfig(string formName)
    {
        return GetAllForms().FirstOrDefault(f => f.FormName == formName);
    }
}
```

---

## 6. UserMasterForm Integration (Enhanced Permissions Tab)

### 6.1 Replace Existing Permissions Tab
- **Remove**: Current basic checkboxes (chkCreateAction, chkReadAction, etc.)
- **Add**: Permission display grid showing effective permissions
- **Add**: "Manage Permissions" button to open PermissionManagementForm

### 6.2 Permission Display Grid
- **Columns**: Form Name, Read, New, Edit, Delete
- **Data**: Shows user's effective permissions
- **Visual Indicators**: 
  - Green: Granted by role
  - Blue: Granted by user override
  - Red: Denied
- **Read-Only**: Display only, editing done in PermissionManagementForm

---

## 7. MainFrame Integration

### 7.1 Ribbon Filtering
```csharp
private void FilterRibbonByPermissions()
{
    var currentUserId = UserManager.Instance.CurrentUser.UserId;
    var visibleForms = PermissionService.Instance.GetVisibleForms(currentUserId);
    
    // Map ribbon buttons to form names
    var buttonFormMap = new Dictionary<string, string>
    {
        { "BtnUserManagement", "UserManagementListForm" },
        { "BtnEstimate", "EstimateForm" },
        { "BtnParams", "ParametersForm" },
        { "BtnDatabase", "DatabaseForm" },
        { "BtnSQLQuery", "SQLQueryForm" }
    };
    
    foreach (var kvp in buttonFormMap)
    {
        var button = ribbonControl.Items[kvp.Key] as DevExpress.XtraBars.BarButtonItem;
        if (button != null)
        {
            button.Visibility = visibleForms.Contains(kvp.Value)
                ? DevExpress.XtraBars.BarItemVisibility.Always
                : DevExpress.XtraBars.BarItemVisibility.Never;
        }
    }
}
```

### 7.2 Form Opening Permission Checks
```csharp
private void BtnEstimate_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
{
    if (!PermissionService.Instance.HasPermission(
        UserManager.Instance.CurrentUser.UserId, "EstimateForm", "Read"))
    {
        MessageBox.Show("Access denied to Estimate Management.", "Permission Error");
        return;
    }
    
    // Open form as usual
    var estimateForm = new EstimateForm();
    estimateForm.MdiParent = this;
    estimateForm.Show();
}
```

---

## 8. Form-Level Permission Integration

### 8.1 Form Load Permission Checks
```csharp
private void EstimateForm_Load(object sender, EventArgs e)
{
    var currentUserId = UserManager.Instance.CurrentUser.UserId;
    
    // Check form access
    if (!PermissionService.Instance.HasPermission(currentUserId, "EstimateForm", "Read"))
    {
        MessageBox.Show("Access denied.", "Permission Error");
        this.Close();
        return;
    }
    
    // Set button states based on permissions
    btnNew.Enabled = PermissionService.Instance.HasPermission(currentUserId, "EstimateForm", "New");
    btnSave.Enabled = PermissionService.Instance.HasPermission(currentUserId, "EstimateForm", "Edit");
    btnDelete.Enabled = PermissionService.Instance.HasPermission(currentUserId, "EstimateForm", "Delete");
}
```

---

## 9. Implementation Timeline

### Phase 1: Database & Core Service (Week 1-2)
- ✅ Create database tables
- ✅ Populate default roles and permissions
- ✅ Implement PermissionService
- ✅ Create FormsConfigService
- ✅ Basic testing

### Phase 2: Permission Management UI (Week 3-4)
- ✅ Create PermissionManagementForm with 2 tabs
- ✅ Implement role permissions grid
- ✅ Implement user permissions grid
- ✅ Add save/load functionality

### Phase 3: Form Integration (Week 5-6)
- ✅ Update UserMasterForm permissions tab
- ✅ Add MainFrame ribbon filtering
- ✅ Add permission checks to all forms
- ✅ Testing and bug fixes

### Phase 4: Polish & Documentation (Week 7-8)
- ✅ User training materials
- ✅ System documentation
- ✅ Performance testing
- ✅ Production deployment

---

## 10. Success Criteria

### 10.1 Functional Requirements
- ✅ Users can only see forms they have Read permission for
- ✅ Form buttons are enabled/disabled based on permissions
- ✅ Permission management is intuitive and easy to use
- ✅ User overrides work correctly over role permissions

### 10.2 Technical Requirements  
- ✅ Permission checks are fast (<100ms)
- ✅ System is reliable and stable
- ✅ Easy to add new forms via config file
- ✅ Clean, maintainable code

### 10.3 User Experience
- ✅ Intuitive permission management interface
- ✅ Clear visual indicators for permission sources
- ✅ Helpful error messages for access denied
- ✅ Minimal training required

---

## 11. What We're NOT Building (Future Enhancements)

The following features are documented separately and may be added later:
- Advanced business rules (amount thresholds, workflow states)
- Compliance features (SOX, GDPR)
- Advanced security (MFA, behavioral analytics)
- API security and mobile support
- Advanced monitoring and analytics

**Focus**: Build a solid, simple, working permission system first. Enhance later based on actual needs.

This implementation plan focuses on your excellent core concept while keeping it practical and achievable.
