# Task 12: Business Forms Permission Integration

## Objective
Integrate permission checks into business forms (EstimateForm and other business data entry forms) to enforce RBAC security at the form level with proper button state management and access control.

## Prerequisites
- Task 05 (Permission Service and Caching) completed
- Task 06 (Permission Helpers and Utilities) completed
- Task 07 (MainFrame Ribbon Integration) completed
- Understanding of existing business form patterns

## Scope
Implement comprehensive permission integration for business forms:
1. EstimateForm permission integration (primary business form)
2. Form access control and validation
3. Button state management based on permissions
4. Data operation restrictions (New/Edit/Delete)
5. Integration with existing form lifecycle patterns

## Deliverables

### 1. EstimateForm Permission Integration
**File**: `ChildForms/EstimateForm.cs` (MODIFY EXISTING)

#### Core Permission Integration
```csharp
// Add to existing EstimateForm class

private bool _hasReadPermission;
private bool _hasNewPermission;
private bool _hasEditPermission;
private bool _hasDeletePermission;

private void EstimateForm_Load(object sender, EventArgs e)
{
    try
    {
        // Check form access permission first
        if (!CheckFormAccess())
            return;

        // Load permissions for current user
        LoadUserPermissions();

        // Existing initialization code...
        InitializeForm();
        LoadEstimateData();

        // Apply permission-based restrictions
        ApplyPermissionRestrictions();

        // Existing code...
    }
    catch (Exception ex)
    {
        // Existing error handling...
    }
}

private bool CheckFormAccess()
{
    try
    {
        if (!UserManager.Instance.IsUserLoggedIn)
        {
            MessageBox.Show("Please log in to access this form.", "Authentication Required");
            this.Close();
            return false;
        }

        var currentUserId = UserManager.Instance.CurrentUser.UserId;
        
        if (!PermissionService.Instance.HasPermission(currentUserId, 
            PermissionConstants.FormNames.EstimateForm, 
            PermissionConstants.PermissionTypes.Read))
        {
            MessageBox.Show("Access denied to Estimate Management.", "Permission Error");
            this.Close();
            return false;
        }

        return true;
    }
    catch (Exception ex)
    {
        Debug.WriteLine($"Error checking form access: {ex.Message}");
        MessageBox.Show("Unable to verify permissions. Access denied.", "Permission Error");
        this.Close();
        return false;
    }
}

private void LoadUserPermissions()
{
    try
    {
        var currentUserId = UserManager.Instance.CurrentUser.UserId;
        var formName = PermissionConstants.FormNames.EstimateForm;

        _hasReadPermission = PermissionService.Instance.HasPermission(currentUserId, formName, PermissionConstants.PermissionTypes.Read);
        _hasNewPermission = PermissionService.Instance.HasPermission(currentUserId, formName, PermissionConstants.PermissionTypes.New);
        _hasEditPermission = PermissionService.Instance.HasPermission(currentUserId, formName, PermissionConstants.PermissionTypes.Edit);
        _hasDeletePermission = PermissionService.Instance.HasPermission(currentUserId, formName, PermissionConstants.PermissionTypes.Delete);

        Debug.WriteLine($"EstimateForm permissions - Read: {_hasReadPermission}, New: {_hasNewPermission}, Edit: {_hasEditPermission}, Delete: {_hasDeletePermission}");
    }
    catch (Exception ex)
    {
        Debug.WriteLine($"Error loading user permissions: {ex.Message}");
        // Default to no permissions on error
        _hasReadPermission = _hasNewPermission = _hasEditPermission = _hasDeletePermission = false;
    }
}

private void ApplyPermissionRestrictions()
{
    try
    {
        // Apply read-only mode if no edit permission
        if (!_hasEditPermission)
        {
            SetFormReadOnly(true);
        }

        // Update button states based on permissions
        UpdateButtonStatesWithPermissions();

        // Apply grid restrictions
        ApplyGridPermissionRestrictions();

        // Update status display
        UpdatePermissionStatusDisplay();
    }
    catch (Exception ex)
    {
        Debug.WriteLine($"Error applying permission restrictions: {ex.Message}");
    }
}

private void SetFormReadOnly(bool readOnly)
{
    try
    {
        // Disable header controls
        foreach (Control control in this.Controls)
        {
            if (control is DevExpress.XtraEditors.TextEdit ||
                control is DevExpress.XtraEditors.DateEdit ||
                control is DevExpress.XtraEditors.LookUpEdit ||
                control is DevExpress.XtraEditors.ComboBoxEdit)
            {
                control.Enabled = !readOnly;
            }
        }

        // Keep grid enabled for navigation but restrict editing
        if (gridEstimateDetails != null)
        {
            viewEstimateDetails.OptionsBehavior.Editable = !readOnly;
            viewEstimateDetails.OptionsView.NewItemRowPosition = readOnly ? 
                DevExpress.XtraGrid.Views.Grid.NewItemRowPosition.None : 
                DevExpress.XtraGrid.Views.Grid.NewItemRowPosition.Bottom;
        }
    }
    catch (Exception ex)
    {
        Debug.WriteLine($"Error setting form read-only: {ex.Message}");
    }
}

private void UpdateButtonStatesWithPermissions()
{
    try
    {
        // Update ribbon buttons if they exist
        if (BarButtonItemNew != null)
            BarButtonItemNew.Enabled = _hasNewPermission;

        if (BarButtonItemSave != null)
            BarButtonItemSave.Enabled = _hasEditPermission && _isDirty;

        if (BarButtonItemDelete != null)
            BarButtonItemDelete.Enabled = _hasDeletePermission && !_isNewMode && _currentEstimate?.EstimateId > 0;

        // Update toolbar buttons if they exist
        if (btnNew != null)
            btnNew.Enabled = _hasNewPermission;

        if (btnSave != null)
            btnSave.Enabled = _hasEditPermission && _isDirty;

        if (btnDelete != null)
            btnDelete.Enabled = _hasDeletePermission && !_isNewMode && _currentEstimate?.EstimateId > 0;

        // Navigation buttons should remain enabled for read access
        if (btnFirst != null)
            btnFirst.Enabled = _hasReadPermission;

        if (btnPrevious != null)
            btnPrevious.Enabled = _hasReadPermission;

        if (btnNext != null)
            btnNext.Enabled = _hasReadPermission;

        if (btnLast != null)
            btnLast.Enabled = _hasReadPermission;
    }
    catch (Exception ex)
    {
        Debug.WriteLine($"Error updating button states: {ex.Message}");
    }
}

private void ApplyGridPermissionRestrictions()
{
    try
    {
        if (viewEstimateDetails == null) return;

        // Configure grid based on permissions
        viewEstimateDetails.OptionsBehavior.Editable = _hasEditPermission;
        viewEstimateDetails.OptionsView.NewItemRowPosition = _hasNewPermission ? 
            DevExpress.XtraGrid.Views.Grid.NewItemRowPosition.Bottom : 
            DevExpress.XtraGrid.Views.Grid.NewItemRowPosition.None;

        // Configure delete operations
        viewEstimateDetails.OptionsSelection.EnableAppearanceFocusedRow = _hasDeletePermission;
        
        // Update context menu if it exists
        if (contextMenuGrid != null)
        {
            foreach (ToolStripItem item in contextMenuGrid.Items)
            {
                switch (item.Name)
                {
                    case "menuAddRow":
                        item.Enabled = _hasNewPermission;
                        break;
                    case "menuDeleteRow":
                        item.Enabled = _hasDeletePermission;
                        break;
                    case "menuEditRow":
                        item.Enabled = _hasEditPermission;
                        break;
                }
            }
        }
    }
    catch (Exception ex)
    {
        Debug.WriteLine($"Error applying grid permission restrictions: {ex.Message}");
    }
}

private void UpdatePermissionStatusDisplay()
{
    try
    {
        var permissionStatus = new List<string>();
        
        if (!_hasNewPermission) permissionStatus.Add("Cannot create new estimates");
        if (!_hasEditPermission) permissionStatus.Add("Read-only mode");
        if (!_hasDeletePermission) permissionStatus.Add("Cannot delete estimates");

        if (permissionStatus.Count > 0)
        {
            var statusMessage = string.Join(" | ", permissionStatus);
            // Update status bar or label if available
            if (lblStatus != null)
            {
                lblStatus.Text = statusMessage;
                lblStatus.Appearance.ForeColor = Color.Orange;
            }
        }
        else
        {
            if (lblStatus != null)
            {
                lblStatus.Text = "Full access";
                lblStatus.Appearance.ForeColor = Color.Green;
            }
        }
    }
    catch (Exception ex)
    {
        Debug.WriteLine($"Error updating permission status display: {ex.Message}");
    }
}
```

#### Data Operation Permission Checks
```csharp
// Modify existing data operation methods

private void BarButtonItemNew_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
{
    try
    {
        if (!_hasNewPermission)
        {
            MessageBox.Show("You do not have permission to create new estimates.", 
                "Permission Denied", MessageBoxButtons.OK, MessageBoxIcon.Warning);
            return;
        }

        // Existing new estimate logic...
        CreateNewEstimate();
    }
    catch (Exception ex)
    {
        // Existing error handling...
    }
}

private void BarButtonItemSave_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
{
    try
    {
        if (!_hasEditPermission)
        {
            MessageBox.Show("You do not have permission to save estimates.", 
                "Permission Denied", MessageBoxButtons.OK, MessageBoxIcon.Warning);
            return;
        }

        // Additional validation for new estimates
        if (_isNewMode && !_hasNewPermission)
        {
            MessageBox.Show("You do not have permission to create new estimates.", 
                "Permission Denied", MessageBoxButtons.OK, MessageBoxIcon.Warning);
            return;
        }

        // Existing save logic...
        SaveEstimate();
    }
    catch (Exception ex)
    {
        // Existing error handling...
    }
}

private void BarButtonItemDelete_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
{
    try
    {
        if (!_hasDeletePermission)
        {
            MessageBox.Show("You do not have permission to delete estimates.", 
                "Permission Denied", MessageBoxButtons.OK, MessageBoxIcon.Warning);
            return;
        }

        if (_currentEstimate?.EstimateId <= 0)
        {
            MessageBox.Show("No estimate selected for deletion.", 
                "No Selection", MessageBoxButtons.OK, MessageBoxIcon.Information);
            return;
        }

        // Existing delete confirmation and logic...
        var result = MessageBox.Show("Are you sure you want to delete this estimate?", 
            "Confirm Delete", MessageBoxButtons.YesNo, MessageBoxIcon.Question);
        
        if (result == DialogResult.Yes)
        {
            DeleteEstimate();
        }
    }
    catch (Exception ex)
    {
        // Existing error handling...
    }
}

// Grid event handlers with permission checks
private void ViewEstimateDetails_InitNewRow(object sender, DevExpress.XtraGrid.Views.Grid.InitNewRowEventArgs e)
{
    try
    {
        if (!_hasNewPermission)
        {
            e.Cancel = true;
            MessageBox.Show("You do not have permission to add new detail rows.", 
                "Permission Denied", MessageBoxButtons.OK, MessageBoxIcon.Warning);
            return;
        }

        // Existing new row initialization...
    }
    catch (Exception ex)
    {
        // Existing error handling...
    }
}

private void ViewEstimateDetails_BeforeLeaveRow(object sender, DevExpress.XtraGrid.Views.Base.RowAllowEventArgs e)
{
    try
    {
        if (!_hasEditPermission && viewEstimateDetails.IsEditing)
        {
            e.Allow = false;
            MessageBox.Show("You do not have permission to edit estimate details.", 
                "Permission Denied", MessageBoxButtons.OK, MessageBoxIcon.Warning);
            return;
        }

        // Existing row validation...
    }
    catch (Exception ex)
    {
        // Existing error handling...
    }
}

private void ViewEstimateDetails_KeyDown(object sender, KeyEventArgs e)
{
    try
    {
        if (e.KeyCode == Keys.Delete && !_hasDeletePermission)
        {
            e.Handled = true;
            MessageBox.Show("You do not have permission to delete detail rows.", 
                "Permission Denied", MessageBoxButtons.OK, MessageBoxIcon.Warning);
            return;
        }

        // Existing key handling...
    }
    catch (Exception ex)
    {
        // Existing error handling...
    }
}
```

### 2. Business Form Helper Extension
**File**: `Modules/Helpers/Forms/BusinessFormPermissionHelper.cs` (NEW)

#### Reusable Permission Helper
```csharp
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using DevExpress.XtraGrid.Views.Grid;
using ProManage.Modules.Services;
using ProManage.Modules.Helpers.Permissions;

namespace ProManage.Modules.Helpers.Forms
{
    public static class BusinessFormPermissionHelper
    {
        public static bool CheckFormAccess(Form form, string formName)
        {
            try
            {
                if (!UserManager.Instance.IsUserLoggedIn)
                {
                    MessageBox.Show("Please log in to access this form.", "Authentication Required");
                    form.Close();
                    return false;
                }

                var currentUserId = UserManager.Instance.CurrentUser.UserId;
                
                if (!PermissionService.Instance.HasPermission(currentUserId, formName, PermissionConstants.PermissionTypes.Read))
                {
                    var displayName = GetFormDisplayName(formName);
                    MessageBox.Show($"Access denied to {displayName}.", "Permission Error");
                    form.Close();
                    return false;
                }

                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error checking form access: {ex.Message}");
                MessageBox.Show("Unable to verify permissions. Access denied.", "Permission Error");
                form.Close();
                return false;
            }
        }

        public static FormPermissions LoadFormPermissions(string formName)
        {
            try
            {
                var currentUserId = UserManager.Instance.CurrentUser.UserId;

                return new FormPermissions
                {
                    CanRead = PermissionService.Instance.HasPermission(currentUserId, formName, PermissionConstants.PermissionTypes.Read),
                    CanNew = PermissionService.Instance.HasPermission(currentUserId, formName, PermissionConstants.PermissionTypes.New),
                    CanEdit = PermissionService.Instance.HasPermission(currentUserId, formName, PermissionConstants.PermissionTypes.Edit),
                    CanDelete = PermissionService.Instance.HasPermission(currentUserId, formName, PermissionConstants.PermissionTypes.Delete)
                };
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error loading form permissions: {ex.Message}");
                return new FormPermissions(); // Default to no permissions
            }
        }

        public static void ApplyFormPermissions(Form form, FormPermissions permissions)
        {
            try
            {
                // Apply read-only mode if no edit permission
                if (!permissions.CanEdit)
                {
                    SetControlsReadOnly(form, true);
                }

                // Update common button names
                UpdateButtonPermissions(form, permissions);

                // Apply grid permissions
                ApplyGridPermissions(form, permissions);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error applying form permissions: {ex.Message}");
            }
        }

        public static void SetControlsReadOnly(Control parent, bool readOnly)
        {
            foreach (Control control in parent.Controls)
            {
                if (control is TextEdit || control is DateEdit || control is LookUpEdit || control is ComboBoxEdit)
                {
                    control.Enabled = !readOnly;
                }
                else if (control.HasChildren)
                {
                    SetControlsReadOnly(control, readOnly);
                }
            }
        }

        public static void UpdateButtonPermissions(Form form, FormPermissions permissions)
        {
            var buttonMappings = new Dictionary<string, Func<FormPermissions, bool>>
            {
                { "btnNew", p => p.CanNew },
                { "btnSave", p => p.CanEdit },
                { "btnDelete", p => p.CanDelete },
                { "BarButtonItemNew", p => p.CanNew },
                { "BarButtonItemSave", p => p.CanEdit },
                { "BarButtonItemDelete", p => p.CanDelete }
            };

            foreach (var mapping in buttonMappings)
            {
                var control = FindControlByName(form, mapping.Key);
                if (control != null)
                {
                    control.Enabled = mapping.Value(permissions);
                }
            }
        }

        public static void ApplyGridPermissions(Form form, FormPermissions permissions)
        {
            var grids = FindControlsOfType<GridView>(form);
            
            foreach (var gridView in grids)
            {
                gridView.OptionsBehavior.Editable = permissions.CanEdit;
                gridView.OptionsView.NewItemRowPosition = permissions.CanNew ? 
                    NewItemRowPosition.Bottom : NewItemRowPosition.None;
            }
        }

        public static void ShowPermissionDeniedMessage(string operation)
        {
            MessageBox.Show($"You do not have permission to {operation}.", 
                "Permission Denied", MessageBoxButtons.OK, MessageBoxIcon.Warning);
        }

        public static string GetPermissionStatusText(FormPermissions permissions)
        {
            var restrictions = new List<string>();
            
            if (!permissions.CanNew) restrictions.Add("Cannot create new records");
            if (!permissions.CanEdit) restrictions.Add("Read-only mode");
            if (!permissions.CanDelete) restrictions.Add("Cannot delete records");

            return restrictions.Count > 0 ? string.Join(" | ", restrictions) : "Full access";
        }

        public static Color GetPermissionStatusColor(FormPermissions permissions)
        {
            if (!permissions.CanEdit) return Color.Orange;
            if (!permissions.CanNew || !permissions.CanDelete) return Color.Blue;
            return Color.Green;
        }

        private static string GetFormDisplayName(string formName)
        {
            var displayNames = new Dictionary<string, string>
            {
                { PermissionConstants.FormNames.EstimateForm, "Estimate Management" },
                // Add other form display names as needed
            };

            return displayNames.TryGetValue(formName, out var displayName) ? displayName : formName;
        }

        private static Control FindControlByName(Control parent, string name)
        {
            if (parent.Name == name) return parent;

            foreach (Control child in parent.Controls)
            {
                var found = FindControlByName(child, name);
                if (found != null) return found;
            }

            return null;
        }

        private static List<T> FindControlsOfType<T>(Control parent) where T : Control
        {
            var controls = new List<T>();
            
            if (parent is T control)
                controls.Add(control);

            foreach (Control child in parent.Controls)
            {
                controls.AddRange(FindControlsOfType<T>(child));
            }

            return controls;
        }
    }

    public class FormPermissions
    {
        public bool CanRead { get; set; }
        public bool CanNew { get; set; }
        public bool CanEdit { get; set; }
        public bool CanDelete { get; set; }

        public FormPermissions()
        {
            CanRead = CanNew = CanEdit = CanDelete = false;
        }
    }
}
```

### 3. Additional Business Forms Integration
**File**: `Modules/Helpers/Forms/BusinessFormIntegrationGuide.md` (NEW)

#### Integration Guide for Other Business Forms
```markdown
# Business Forms Permission Integration Guide

## Overview
This guide provides instructions for integrating permission checks into additional business forms following the patterns established in EstimateForm.

## Integration Steps

### 1. Form Load Integration
```csharp
private void Form_Load(object sender, EventArgs e)
{
    try
    {
        // Check form access
        if (!BusinessFormPermissionHelper.CheckFormAccess(this, "YourFormName"))
            return;

        // Load permissions
        var permissions = BusinessFormPermissionHelper.LoadFormPermissions("YourFormName");
        
        // Apply permissions
        BusinessFormPermissionHelper.ApplyFormPermissions(this, permissions);
        
        // Existing initialization...
    }
    catch (Exception ex)
    {
        // Error handling...
    }
}
```

### 2. Button Event Integration
```csharp
private void BtnNew_Click(object sender, EventArgs e)
{
    if (!_permissions.CanNew)
    {
        BusinessFormPermissionHelper.ShowPermissionDeniedMessage("create new records");
        return;
    }
    
    // Existing logic...
}
```

### 3. Data Operation Validation
```csharp
private void SaveData()
{
    if (!_permissions.CanEdit)
    {
        BusinessFormPermissionHelper.ShowPermissionDeniedMessage("save changes");
        return;
    }
    
    // Existing save logic...
}
```

## Form Names to Use
- EstimateForm: "EstimateForm"
- Add other business forms as they are identified

## Testing Checklist
- [ ] Form access denied for users without Read permission
- [ ] New button disabled for users without New permission
- [ ] Save operations blocked for users without Edit permission
- [ ] Delete operations blocked for users without Delete permission
- [ ] Grid editing restricted based on permissions
- [ ] Status messages show permission restrictions
```

## Acceptance Criteria

### EstimateForm Integration
- [ ] Form access controlled by Read permission
- [ ] New/Edit/Delete operations restricted by permissions
- [ ] Button states reflect user permissions
- [ ] Grid editing controlled by permissions
- [ ] Clear permission status display

### Helper Implementation
- [ ] Reusable helper for other business forms
- [ ] Consistent permission checking patterns
- [ ] Proper error handling and user feedback
- [ ] Performance optimized for form operations

### User Experience
- [ ] Clear permission denied messages
- [ ] Intuitive button state management
- [ ] Visual indicators for permission restrictions
- [ ] No functional degradation for authorized users

### Security Enforcement
- [ ] No bypass of permission checks possible
- [ ] Comprehensive coverage of all data operations
- [ ] Proper validation of user permissions
- [ ] Audit logging of permission violations

## Integration Points

### Existing System
- Uses established EstimateForm patterns
- Integrates with existing button and grid controls
- Compatible with current form lifecycle
- Follows ProManage error handling conventions

### Future Tasks
- Provides foundation for Task 13 (System Forms integration)
- Establishes patterns for all business form security
- Supports comprehensive permission auditing
- Enables consistent security across application

## Testing Requirements

### Functional Testing
```csharp
[Test]
public void EstimateForm_NoReadPermission_ClosesForm()
{
    // Test that form closes for users without read permission
}

[Test]
public void EstimateForm_NoEditPermission_DisablesSaveButton()
{
    // Test that save button is disabled without edit permission
}
```

### Security Testing
- Test permission bypass attempts
- Validate all data operation restrictions
- Test with different user roles
- Verify audit logging of violations

### Integration Testing
- Test with existing EstimateForm functionality
- Validate helper integration with other forms
- Test performance impact of permission checks
- Verify compatibility with existing patterns

## Implementation Notes

### Performance Considerations
- Cache permissions during form lifecycle
- Minimize permission service calls
- Efficient button state updates
- Optimized grid permission application

### Security Best Practices
- Fail-safe approach: deny access on errors
- Comprehensive permission validation
- Clear audit trail for violations
- No client-side permission bypasses

### Maintainability
- Reusable helper patterns
- Consistent error handling
- Clear separation of permission logic
- Extensible design for future forms

## Files to Create/Modify

### Modified Files (1 file)
- `ChildForms/EstimateForm.cs` (MODIFY - add permission integration)

### New Files (2 files)
- `Modules/Helpers/Forms/BusinessFormPermissionHelper.cs`
- `Modules/Helpers/Forms/BusinessFormIntegrationGuide.md`

## Dependencies
- Task 05: Permission Service and Caching (REQUIRED)
- Task 06: Permission Helpers and Utilities (REQUIRED)
- Task 07: MainFrame Ribbon Integration (REQUIRED)
- Existing EstimateForm implementation

## Estimated Effort
- 6-8 hours for EstimateForm integration
- 4-6 hours for helper implementation
- 2-4 hours for integration guide and documentation
- 4-6 hours for testing and validation
