# Task 07: MainFrame Ribbon Permission Integration

## Objective
Integrate the RBAC permission system with MainFrame's ribbon controls to dynamically filter form visibility and implement permission-based form opening checks.

## Prerequisites
- Task 05 (Permission Service and Caching) completed
- Task 06 (Permission Helpers and Utilities) completed
- Understanding of existing MainFrame ribbon structure
- Access to current MainFrame.cs implementation

## Scope
Implement comprehensive MainFrame integration:
1. Dynamic ribbon button filtering based on user permissions
2. Permission checks for all form opening operations
3. Integration with existing UserManager service
4. Graceful error handling for access denied scenarios
5. Performance optimization for ribbon updates

## Deliverables

### 1. MainFrame Permission Integration
**File**: `MainFrame.cs` (MODIFY EXISTING)

#### Core Integration Methods
```csharp
// Add to existing MainFrame class
private void FilterRibbonByPermissions()
{
    try
    {
        if (!UserManager.Instance.IsUserLoggedIn) return;

        var currentUserId = UserManager.Instance.CurrentUser.UserId;
        var visibleForms = PermissionService.Instance.GetVisibleForms(currentUserId);

        // Map ribbon buttons to form names
        var buttonFormMap = GetRibbonButtonFormMapping();

        foreach (var kvp in buttonFormMap)
        {
            var button = FindRibbonButton(kvp.Key);
            if (button != null)
            {
                button.Visibility = visibleForms.Contains(kvp.Value)
                    ? DevExpress.XtraBars.BarItemVisibility.Always
                    : DevExpress.XtraBars.BarItemVisibility.Never;
            }
        }

        // Refresh ribbon layout
        ribbonControl.Invalidate();
    }
    catch (Exception ex)
    {
        Debug.WriteLine($"Error filtering ribbon by permissions: {ex.Message}");
        // Log error but don't break the UI
    }
}

private Dictionary<string, string> GetRibbonButtonFormMapping()
{
    return new Dictionary<string, string>
    {
        { "BtnUserManagement", PermissionConstants.FormNames.UserManagementListForm },
        { "BtnRoleManagement", PermissionConstants.FormNames.RoleMasterForm },
        { "BtnEstimate", PermissionConstants.FormNames.EstimateForm },
        { "BtnParams", PermissionConstants.FormNames.ParametersForm },
        { "BtnDatabase", PermissionConstants.FormNames.DatabaseForm },
        { "BtnSQLQuery", PermissionConstants.FormNames.SQLQueryForm },
        { "BtnPermissionManagement", PermissionConstants.FormNames.PermissionManagementForm }
    };
}

private DevExpress.XtraBars.BarButtonItem FindRibbonButton(string buttonName)
{
    return ribbonControl.Items[buttonName] as DevExpress.XtraBars.BarButtonItem;
}
```

#### Permission-Aware Form Opening
```csharp
// Modify existing form opening methods
private void BtnUserManagement_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
{
    try
    {
        if (!CheckFormPermission(PermissionConstants.FormNames.UserManagementListForm, PermissionConstants.PermissionTypes.Read))
            return;

        var userManagementListForm = new ListForms.UserManagementListForm();
        var openedForm = OpenChildForm(userManagementListForm, "User Management");
    }
    catch (Exception ex)
    {
        HandleFormOpenError(ex, "User Management");
    }
}

private void BtnEstimate_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
{
    try
    {
        if (!CheckFormPermission(PermissionConstants.FormNames.EstimateForm, PermissionConstants.PermissionTypes.Read))
            return;

        var estimateForm = new ChildForms.EstimateForm();
        var openedForm = OpenChildForm(estimateForm, "Estimate Management");
    }
    catch (Exception ex)
    {
        HandleFormOpenError(ex, "Estimate Management");
    }
}

private void BtnDatabase_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
{
    try
    {
        if (!CheckFormPermission(PermissionConstants.FormNames.DatabaseForm, PermissionConstants.PermissionTypes.Read))
            return;

        var databaseForm = new MainForms.DatabaseForm();
        var openedForm = OpenChildForm(databaseForm, "Database Configuration");
    }
    catch (Exception ex)
    {
        HandleFormOpenError(ex, "Database Configuration");
    }
}

// Add similar modifications for all other form opening methods
```

#### Permission Check Helper
```csharp
private bool CheckFormPermission(string formName, string permissionType)
{
    try
    {
        if (!UserManager.Instance.IsUserLoggedIn)
        {
            ShowAccessDeniedMessage("Please log in to access this feature.");
            return false;
        }

        var currentUserId = UserManager.Instance.CurrentUser.UserId;
        
        if (!PermissionService.Instance.HasPermission(currentUserId, formName, permissionType))
        {
            ShowAccessDeniedMessage($"Access denied to {GetFormDisplayName(formName)}.");
            return false;
        }

        return true;
    }
    catch (Exception ex)
    {
        Debug.WriteLine($"Error checking form permission: {ex.Message}");
        ShowAccessDeniedMessage("Unable to verify permissions. Please try again.");
        return false;
    }
}

private void ShowAccessDeniedMessage(string message)
{
    MessageBox.Show(message, "Permission Error", MessageBoxButtons.OK, MessageBoxIcon.Warning);
}

private string GetFormDisplayName(string formName)
{
    var displayNames = new Dictionary<string, string>
    {
        { PermissionConstants.FormNames.EstimateForm, "Estimate Management" },
        { PermissionConstants.FormNames.UserManagementListForm, "User Management" },
        { PermissionConstants.FormNames.UserMasterForm, "User Entry" },
        { PermissionConstants.FormNames.DatabaseForm, "Database Configuration" },
        { PermissionConstants.FormNames.ParametersForm, "System Parameters" },
        { PermissionConstants.FormNames.RoleMasterForm, "Role Management" },
        { PermissionConstants.FormNames.SQLQueryForm, "SQL Query Tool" },
        { PermissionConstants.FormNames.PermissionManagementForm, "Permission Management" }
    };

    return displayNames.TryGetValue(formName, out var displayName) ? displayName : formName;
}
```

### 2. User Login/Logout Integration
**File**: `MainFrame.cs` (MODIFY EXISTING)

#### Event Handler Integration
```csharp
// Modify existing InitializeUI method
private void InitializeUI()
{
    try
    {
        // Existing initialization code...

        // Subscribe to user events for permission updates
        SubscribeToUserEvents();

        // Apply initial permission filtering
        FilterRibbonByPermissions();

        // Existing code...
    }
    catch (Exception ex)
    {
        // Existing error handling...
    }
}

private void SubscribeToUserEvents()
{
    // Subscribe to UserManager events if they exist
    // If not, we'll handle this in the login/logout methods
    UserManager.Instance.UserLoggedIn += OnUserLoggedIn;
    UserManager.Instance.UserLoggedOut += OnUserLoggedOut;
    UserManager.Instance.UserChanged += OnUserChanged;
}

private void OnUserLoggedIn(object sender, EventArgs e)
{
    try
    {
        // Refresh permissions for the logged-in user
        var currentUserId = UserManager.Instance.CurrentUser.UserId;
        PermissionService.Instance.RefreshUserPermissions(currentUserId);

        // Update ribbon visibility
        FilterRibbonByPermissions();

        // Update status or other UI elements as needed
        UpdateUserStatusDisplay();
    }
    catch (Exception ex)
    {
        Debug.WriteLine($"Error handling user login: {ex.Message}");
    }
}

private void OnUserLoggedOut(object sender, EventArgs e)
{
    try
    {
        // Hide all permission-controlled buttons
        HideAllPermissionControlledButtons();

        // Clear any cached permissions
        PermissionService.Instance.ClearCache();

        // Update status display
        UpdateUserStatusDisplay();
    }
    catch (Exception ex)
    {
        Debug.WriteLine($"Error handling user logout: {ex.Message}");
    }
}

private void OnUserChanged(object sender, EventArgs e)
{
    try
    {
        // Refresh permissions for the new user
        if (UserManager.Instance.IsUserLoggedIn)
        {
            var currentUserId = UserManager.Instance.CurrentUser.UserId;
            PermissionService.Instance.RefreshUserPermissions(currentUserId);
            FilterRibbonByPermissions();
        }
        else
        {
            HideAllPermissionControlledButtons();
        }

        UpdateUserStatusDisplay();
    }
    catch (Exception ex)
    {
        Debug.WriteLine($"Error handling user change: {ex.Message}");
    }
}
```

#### Ribbon Management Methods
```csharp
private void HideAllPermissionControlledButtons()
{
    var buttonFormMap = GetRibbonButtonFormMapping();
    
    foreach (var buttonName in buttonFormMap.Keys)
    {
        var button = FindRibbonButton(buttonName);
        if (button != null)
        {
            button.Visibility = DevExpress.XtraBars.BarItemVisibility.Never;
        }
    }

    ribbonControl.Invalidate();
}

private void UpdateUserStatusDisplay()
{
    try
    {
        if (UserManager.Instance.IsUserLoggedIn)
        {
            var user = UserManager.Instance.CurrentUser;
            // Update status bar or other UI elements with user info
            // This depends on existing UI structure
        }
        else
        {
            // Clear user status display
        }
    }
    catch (Exception ex)
    {
        Debug.WriteLine($"Error updating user status display: {ex.Message}");
    }
}
```

### 3. Performance Optimization
**File**: `Modules/Helpers/MainFrame/RibbonPermissionManager.cs` (NEW)

#### Dedicated Ribbon Manager
```csharp
public class RibbonPermissionManager
{
    private readonly MainFrame _mainFrame;
    private readonly Dictionary<string, string> _buttonFormMap;
    private readonly Dictionary<string, DevExpress.XtraBars.BarButtonItem> _buttonCache;

    public RibbonPermissionManager(MainFrame mainFrame)
    {
        _mainFrame = mainFrame;
        _buttonFormMap = GetButtonFormMapping();
        _buttonCache = new Dictionary<string, DevExpress.XtraBars.BarButtonItem>();
        CacheRibbonButtons();
    }

    public void UpdateRibbonPermissions(int userId)
    {
        try
        {
            var visibleForms = PermissionService.Instance.GetVisibleForms(userId);
            
            foreach (var kvp in _buttonFormMap)
            {
                if (_buttonCache.TryGetValue(kvp.Key, out var button))
                {
                    button.Visibility = visibleForms.Contains(kvp.Value)
                        ? DevExpress.XtraBars.BarItemVisibility.Always
                        : DevExpress.XtraBars.BarItemVisibility.Never;
                }
            }

            _mainFrame.ribbonControl.Invalidate();
        }
        catch (Exception ex)
        {
            Debug.WriteLine($"Error updating ribbon permissions: {ex.Message}");
        }
    }

    public void HideAllButtons()
    {
        foreach (var button in _buttonCache.Values)
        {
            button.Visibility = DevExpress.XtraBars.BarItemVisibility.Never;
        }
        
        _mainFrame.ribbonControl.Invalidate();
    }

    private void CacheRibbonButtons()
    {
        foreach (var buttonName in _buttonFormMap.Keys)
        {
            var button = _mainFrame.ribbonControl.Items[buttonName] as DevExpress.XtraBars.BarButtonItem;
            if (button != null)
            {
                _buttonCache[buttonName] = button;
            }
        }
    }

    private Dictionary<string, string> GetButtonFormMapping()
    {
        return new Dictionary<string, string>
        {
            { "BtnUserManagement", PermissionConstants.FormNames.UserManagementListForm },
            { "BtnRoleManagement", PermissionConstants.FormNames.RoleMasterForm },
            { "BtnEstimate", PermissionConstants.FormNames.EstimateForm },
            { "BtnParams", PermissionConstants.FormNames.ParametersForm },
            { "BtnDatabase", PermissionConstants.FormNames.DatabaseForm },
            { "BtnSQLQuery", PermissionConstants.FormNames.SQLQueryForm },
            { "BtnPermissionManagement", PermissionConstants.FormNames.PermissionManagementForm }
        };
    }
}
```

### 4. Error Handling and Logging
**File**: `Modules/Helpers/MainFrame/MainFramePermissionHelper.cs` (NEW)

#### Centralized Error Handling
```csharp
public static class MainFramePermissionHelper
{
    public static bool SafeCheckPermission(int userId, string formName, string permissionType)
    {
        try
        {
            return PermissionService.Instance.HasPermission(userId, formName, permissionType);
        }
        catch (Exception ex)
        {
            Debug.WriteLine($"Error checking permission for user {userId}, form {formName}, permission {permissionType}: {ex.Message}");
            return false; // Fail safe - deny access on error
        }
    }

    public static void HandleFormOpenError(Exception ex, string formName)
    {
        Debug.WriteLine($"Error opening form {formName}: {ex.Message}");
        
        var message = ex is UnauthorizedAccessException 
            ? $"Access denied to {formName}."
            : $"Unable to open {formName}. Please try again.";
            
        MessageBox.Show(message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
    }

    public static void LogPermissionCheck(int userId, string formName, string permissionType, bool result)
    {
        try
        {
            PermissionAuditHelper.LogPermissionCheck(userId, formName, permissionType, result);
        }
        catch (Exception ex)
        {
            Debug.WriteLine($"Error logging permission check: {ex.Message}");
            // Don't throw - logging failures shouldn't break functionality
        }
    }
}
```

## Acceptance Criteria

### Ribbon Integration
- [ ] Ribbon buttons filtered based on user permissions
- [ ] Only forms with Read permission appear in ribbon
- [ ] Ribbon updates immediately on user login/logout
- [ ] Performance impact minimal (under 100ms for ribbon update)

### Form Opening Security
- [ ] All form opening methods check permissions
- [ ] Clear error messages for access denied scenarios
- [ ] Graceful handling of permission check failures
- [ ] No forms can be opened without proper permissions

### User Event Integration
- [ ] Ribbon updates on user login/logout/change
- [ ] Permission cache refreshed appropriately
- [ ] No memory leaks from event subscriptions
- [ ] Proper cleanup on form disposal

### Performance Requirements
- [ ] Ribbon filtering completes under 100ms
- [ ] Permission checks complete under 10ms
- [ ] No UI freezing during permission updates
- [ ] Efficient caching of ribbon button references

### Error Handling
- [ ] Graceful degradation on permission service failures
- [ ] Clear error messages for users
- [ ] Comprehensive logging for debugging
- [ ] No application crashes from permission errors

## Integration Points

### Existing System
- Integrates with existing MainFrame ribbon structure
- Uses current UserManager service for user context
- Compatible with existing form opening patterns
- Follows established error handling conventions

### Future Tasks
- Provides foundation for Task 08 (UserMasterForm integration)
- Supports Task 12 (Business Forms integration)
- Enables Task 13 (System Forms integration)
- Establishes pattern for all form permission checks

## Testing Requirements

### Functional Testing
```csharp
[Test]
public void FilterRibbonByPermissions_AdminUser_ShowsAllButtons()
{
    // Test that admin user sees all ribbon buttons
}

[Test]
public void CheckFormPermission_UnauthorizedUser_ReturnsFalse()
{
    // Test that unauthorized users cannot open forms
}

[Test]
public void OnUserLoggedOut_HidesAllButtons()
{
    // Test that logout hides all permission-controlled buttons
}
```

### Performance Testing
- Test ribbon update performance with multiple buttons
- Validate permission check performance
- Test memory usage during user switching
- Monitor UI responsiveness during permission updates

### Integration Testing
- Test with different user roles and permissions
- Validate error handling scenarios
- Test user login/logout scenarios
- Verify ribbon state consistency

## Implementation Notes

### Security Considerations
- Fail-safe approach: deny access on permission check errors
- Comprehensive audit logging of permission checks
- No client-side permission bypasses possible
- Clear separation between UI filtering and security enforcement

### Performance Optimization
- Cache ribbon button references for fast updates
- Minimize permission service calls during ribbon updates
- Use efficient data structures for button mapping
- Optimize ribbon invalidation calls

### Maintainability
- Centralized button-to-form mapping
- Consistent error handling patterns
- Clear separation of concerns
- Extensible design for future forms

## Files to Create/Modify

### Modified Files (1 file)
- `MainFrame.cs` (MODIFY - add permission integration methods)

### New Files (2 files)
- `Modules/Helpers/MainFrame/RibbonPermissionManager.cs`
- `Modules/Helpers/MainFrame/MainFramePermissionHelper.cs`

## Dependencies
- Task 05: Permission Service and Caching (REQUIRED)
- Task 06: Permission Helpers and Utilities (REQUIRED)
- Existing MainFrame.cs implementation
- Existing UserManager service

## Estimated Effort
- 4-6 hours for MainFrame integration
- 2-4 hours for ribbon permission manager
- 2-3 hours for error handling and helpers
- 3-4 hours for testing and validation
